// 包围盒接口
export interface BoundingBox {
    minX: number;
    minY: number;
    maxX: number;
    maxY: number;
}

// 四叉树节点数据
export interface QuadTreeNode {
    id: string;
    bounds: BoundingBox;
}

// 四叉树实现
export class QuadTree {
    private bounds: BoundingBox;
    private nodes: QuadTreeNode[] = [];
    private children: QuadTree[] = [];
    private maxNodes: number;
    private maxDepth: number;
    private depth: number;
    private divided: boolean = false;

    constructor(bounds: BoundingBox, maxNodes = 10, maxDepth = 5, depth = 0) {
        this.bounds = bounds;
        this.maxNodes = maxNodes;
        this.maxDepth = maxDepth;
        this.depth = depth;
    }

    // 检查点是否在边界内
    private contains(point: { x: number; y: number }): boolean {
        return point.x >= this.bounds.minX && point.x <= this.bounds.maxX &&
               point.y >= this.bounds.minY && point.y <= this.bounds.maxY;
    }

    // 检查包围盒是否相交
    private intersects(range: BoundingBox): boolean {
        const c1 = range.maxX < this.bounds.minX;
        const c2 = range.minX > this.bounds.maxX;
        const c3 = range.maxY < this.bounds.minY;
        const c4 = range.minY > this.bounds.maxY;

        const orResult = c1 || c2 || c3 || c4;
        const result = !orResult;

        return result;
    }

    // 检查两个包围盒是否相交
    private boundsIntersect(bounds1: BoundingBox, bounds2: BoundingBox): boolean {
        return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                 bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
    }

    // 检查包围盒是否包含另一个包围盒
    private containsBounds(bounds: BoundingBox): boolean {
        return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
               bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
    }

    // 分割四叉树
    private subdivide(): void {
        if (this.divided || this.depth >= this.maxDepth) return;

        const { minX, minY, maxX, maxY } = this.bounds;
        const midX = (minX + maxX) / 2;
        const midY = (minY + maxY) / 2;

        // 创建四个子象限
        this.children = [
            // 西北象限
            new QuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            // 东北象限
            new QuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            // 西南象限
            new QuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
            // 东南象限
            new QuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
        ];

        this.divided = true;

        // 将现有节点重新分配到子象限
        const nodesToRedistribute = [...this.nodes];
        this.nodes = [];

        for (const node of nodesToRedistribute) {
            this.insert(node);
        }
    }

    // 插入节点
    insert(node: QuadTreeNode): boolean {
        // 检查节点是否在当前边界内
        if (!this.containsBounds(node.bounds)) {
            return false;
        }

        // 如果当前节点数量未达到上限且未分割，直接添加
        if (this.nodes.length < this.maxNodes && !this.divided) {
            this.nodes.push(node);
            return true;
        }

        // 需要分割
        if (!this.divided) {
            this.subdivide();
        }

        // 尝试插入到子象限
        for (const child of this.children) {
            if (child.insert(node)) {
                return true;
            }
        }

        // 如果无法插入到子象限，保留在当前层级
        this.nodes.push(node);
        return true;
    }

    // 查询与指定范围相交的节点
    query(range: BoundingBox, found: QuadTreeNode[] = []): QuadTreeNode[] {
        // 检查查询范围是否与当前边界相交
        if (!this.intersects(range)) {
            return found;
        }

        // 检查当前层级的节点
        for (const node of this.nodes) {
            if (this.boundsIntersect(range, node.bounds)) {
                found.push(node);
            }
        }

        // 递归查询子象限
        if (this.divided) {
            for (const child of this.children) {
                child.query(range, found);
            }
        }

        return found;
    }

    // 删除节点
    remove(nodeId: string): boolean {
        // 在当前层级查找并删除
        const index = this.nodes.findIndex(node => node.id === nodeId);
        if (index !== -1) {
            this.nodes.splice(index, 1);
            return true;
        }

        // 在子象限中查找并删除
        if (this.divided) {
            for (const child of this.children) {
                if (child.remove(nodeId)) {
                    return true;
                }
            }
        }

        return false;
    }

    // 更新节点位置
    update(nodeId: string, newBounds: BoundingBox): boolean {
        // 先删除旧节点
        if (this.remove(nodeId)) {
            // 重新插入新位置
            return this.insert({ id: nodeId, bounds: newBounds });
        }
        return false;
    }

    // 清空四叉树
    clear(): void {
        this.nodes = [];
        this.children = [];
        this.divided = false;
    }

    // 获取统计信息
    getStats(): { totalNodes: number; depth: number; leafNodes: number } {
        let totalNodes = this.nodes.length;
        let maxDepth = this.depth;
        let leafNodes = this.divided ? 0 : 1;

        if (this.divided) {
            for (const child of this.children) {
                const childStats = child.getStats();
                totalNodes += childStats.totalNodes;
                maxDepth = Math.max(maxDepth, childStats.depth);
                leafNodes += childStats.leafNodes;
            }
        }

        return { totalNodes, depth: maxDepth, leafNodes };
    }
}

// 创建四叉树的工厂函数
export function createQuadTree(bounds: BoundingBox, maxNodes = 10, maxDepth = 5): QuadTree {
    return new QuadTree(bounds, maxNodes, maxDepth);
}
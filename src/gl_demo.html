<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebGL2 树形结构渲染器演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ccc;
            background: white;
            display: block;
            margin: 20px auto;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            padding: 12px 24px;
            margin: 8px;
            border: none;
            background: #007acc;
            color: white;
            cursor: pointer;
            border-radius: 6px;
            font-size: 14px;
            transition: background 0.3s;
        }
        button:hover {
            background: #005a9e;
        }
        button:active {
            background: #004080;
        }
        .performance-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.6;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        .feature-list {
            background: #e8f4fd;
            border-left: 4px solid #007acc;
            padding: 15px;
            margin: 15px 0;
        }
        .feature-list h3 {
            margin-top: 0;
            color: #007acc;
        }
        .feature-list ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .feature-list li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 WebGL2 树形结构渲染器演示</h1>

        <div class="demo-section">
            <div class="feature-list">
                <h3>🎯 核心特性</h3>
                <ul>
                    <li><strong>四叉树空间索引</strong>：支持动态增删改查，按视口范围快速查询可见节点</li>
                    <li><strong>双向Buffer分配</strong>：固定长度数据从前向后分配，动态长度数据从后向前分配</li>
                    <li><strong>视口裁剪渲染</strong>：只渲染视口内可见节点，大幅提升大数据量场景性能</li>
                    <li><strong>批量渲染优化</strong>：单次drawElements调用渲染所有可见节点</li>
                    <li><strong>增量更新支持</strong>：支持节点的动态增删改，无需全量重建</li>
                </ul>
            </div>

            <canvas id="canvas" width="800" height="600"></canvas>

            <div class="controls">
                <button onclick="renderSimpleTree()">🌳 简单树形结构</button>
                <button onclick="renderComplexTree()">🌲 复杂树形结构</button>
                <button onclick="renderAnimatedTree()">🎬 动画演示</button>
                <button onclick="renderMassiveTree()">📊 大数据量测试</button>
                <button onclick="renderViewportCulling()">🔍 视口裁剪演示</button>
                <button onclick="renderMassiveAnimatedTree()">⚡ 大数据量动画</button>
            </div>

            <div id="performance-info" class="performance-info">
                点击上方按钮开始演示...
            </div>

            <div class="description">
                <h3>📖 使用说明</h3>
                <p><strong>简单树形结构</strong>：展示基本的树形节点渲染，包含父子关系和变换矩阵。</p>
                <p><strong>复杂树形结构</strong>：展示多层嵌套的树形结构，测试递归渲染能力。</p>
                <p><strong>动画演示</strong>：展示实时动画渲染，包含旋转变换和FPS统计。</p>
                <p><strong>大数据量测试</strong>：生成10万个节点测试渲染性能和视口裁剪效果。</p>
                <p><strong>视口裁剪演示</strong>：展示视口裁剪功能，只渲染可见区域内的节点。</p>
                <p><strong>大数据量动画</strong>：5万节点的实时动画，测试动态渲染性能。</p>

                <h3>🎮 交互操作</h3>
                <p><strong>滚轮缩放</strong>：在视口裁剪演示中，使用鼠标滚轮可以缩放视图，观察裁剪效果的实时变化。</p>
                <p><strong>性能监控</strong>：实时显示总节点数、可见节点数、裁剪效率、FPS等性能指标。</p>
            </div>
        </div>

        <div class="demo-section">
            <h3>🏗️ 技术架构</h3>
            <div class="description">
                <p><strong>quad_tree.ts</strong>：四叉树空间索引，支持按范围建立、动态增删、视口查询。</p>
                <p><strong>gl_buffer.ts</strong>：Buffer数据管理，双向分配策略，节点缓存，索引生成。</p>
                <p><strong>gl.ts</strong>：纯WebGL渲染器，负责着色器管理和绘制调用。</p>
                <p><strong>数据流</strong>：root节点 → 视口计算 → 四叉树查询 → 索引生成 → WebGL渲染。</p>
            </div>
        </div>
    </div>

    <script type="module" src="gl_demo.js"></script>
</body>
</html>
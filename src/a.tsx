/** @jsx JSX_h */

function JSX_h(){

}
// 1. 定义 Text 的基础 props
interface TextProps {
  data: string;
}

// 2. 定义 Box 子节点类型（Text 支持 xy）
interface WithXY {
  x?: number;
  y?: number;
}
type BoxChild = JSX.Element & WithXY;

// 3. 自定义 JSX 命名空间
declare namespace JSX {
  // Text 默认不支持 xy
  type IntrinsicAttributes = {

  }
  interface IntrinsicElements {

  }

  type Element = any;
}

function Box(props?:any) {
  return undefined;
}

function MyText(props: TextProps) {
  return undefined;
}

//Box实现孩子布局,会使用子组件的xy属性, 对于嵌套组件MyNode, 其最终的组件实例对象为text组件, 布局时是使用MyNode的直接属性还是最终实例对象MyText的xy属性
// 点击交互时, 需要向上派发给父组件, 是否通过parent属性记录父组件对象
// 对于嵌套组件MyNode, 最终的组件实例是text组件, 中间可能隔了很多层, 是否将最终的组件对象设置为MyNode组件某个属性
// 如何组件支持点击事件响应, 如果嵌套组件每一层都增加click监听, 点击时是否每一层都触发
const app = (
    <MyNode x={50} y={130} onClick={onclick} />  // 正确
);
function MyNode(){
  return <NodeA  onClick={onclick}/>
}
function NodeA(){
  return <text data="out" x={10} y={20}  onClick={onclick}/>
}

function onclick(){

}

const text = <MyText data="out" />;       // 正确
const text2 = <MyText data="out" x={1} y={2} />; // 报错，Text 不支持 x/y
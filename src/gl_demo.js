import { createGlRenderer, createTestNode, rect_points4_byRect } from './gl.js';

let renderer;
let animationId;

// 更新性能信息显示
function updatePerformanceInfo(html) {
    const performanceInfo = document.getElementById('performance-info');
    if (performanceInfo) {
        performanceInfo.innerHTML = html;
    }
}

// 初始化渲染器
function initRenderer() {
    if (animationId) {
        cancelAnimationFrame(animationId);
        animationId = null;
    }

    const canvas = document.getElementById('canvas');
    if (!renderer) {
        renderer = createGlRenderer(canvas);
    }
}

// 创建简单树形结构
window.renderSimpleTree = function () {
    initRenderer();

    const treeData = {
        id: 'root',
        mat: [1, 0, 0, 1, 100, 100],
        children: [
            createTestNode([1, 0, 0, 1, 0, 0], [255, 0, 0, 255], 'red_node'), // 红色根节点
            {
                id: 'branch_1',
                mat: [1, 0, 0, 1, 150, 0],
                children: [
                    createTestNode([1, 0, 0, 1, 0, 0], [0, 255, 0, 255], 'green_node'), // 绿色子节点
                    createTestNode([1, 0, 0, 1, 0, 120], [0, 0, 255, 255], 'blue_node') // 蓝色子节点
                ]
            }
        ]
    };

    const stats = renderer.render(treeData);
    updatePerformanceInfo(`
        简单树形结构渲染完成<br>
        总节点数: ${stats.totalNodes}<br>
        可见节点数: ${stats.visibleNodes}<br>
        视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}<br>
        缩放: ${stats.viewport.scale.toFixed(2)}x
    `);
};

// 创建复杂树形结构
window.renderComplexTree = function () {
    initRenderer();

    const colors = [
        [255, 100, 100, 255],
        [100, 255, 100, 255],
        [100, 100, 255, 255],
        [255, 255, 100, 255],
        [255, 100, 255, 255],
        [100, 255, 255, 255]
    ];

    function createBranch(depth, x, y, color, index) {
        if (depth <= 0) return null;

        const node = {
            id: `branch_${depth}_${index}`,
            mat: [0.8, 0, 0, 0.8, x, y],
            glData: {
                vertex: rect_points4_byRect({ width: 60, height: 60 }),
                rgba: color
            }
        };

        if (depth > 1) {
            node.children = [
                createBranch(depth - 1, 80, -20, colors[(depth + 1) % colors.length], index * 2),
                createBranch(depth - 1, 80, 80, colors[(depth + 2) % colors.length], index * 2 + 1)
            ].filter(Boolean);
        }

        return node;
    }

    const treeData = {
        id: 'complex_root',
        mat: [1, 0, 0, 1, 50, 200],
        children: [createBranch(4, 0, 0, colors[0], 1)]
    };

    const stats = renderer.render(treeData);
    updatePerformanceInfo(`
        复杂树形结构渲染完成<br>
        总节点数: ${stats.totalNodes}<br>
        可见节点数: ${stats.visibleNodes}<br>
        视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}<br>
        缩放: ${stats.viewport.scale.toFixed(2)}x<br>
        裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%
    `);
};

// 创建动画树形结构
window.renderAnimatedTree = function () {
    initRenderer();

    if (animationId) {
        cancelAnimationFrame(animationId);
    }

    let time = 0;
    let frameCount = 0;
    let lastFpsTime = performance.now();

    function animate() {
        time += 0.02;
        frameCount++;

        const treeData = {
            id: 'animated_root',
            mat: [1, 0, 0, 1, 200, 200],
            children: [
                {
                    id: 'rotating_parent',
                    mat: [Math.cos(time), Math.sin(time), -Math.sin(time), Math.cos(time), 0, 0],
                    glData: {
                        vertex: rect_points4_byRect({ width: 80, height: 80 }),
                        rgba: [255, 100, 100, 255]
                    },
                    children: [
                        {
                            id: 'child_1',
                            mat: [1, 0, 0, 1, 100, 0],
                            glData: {
                                vertex: rect_points4_byRect({ width: 40, height: 40 }),
                                rgba: [100, 255, 100, 255]
                            }
                        },
                        {
                            id: 'child_2',
                            mat: [Math.cos(time * 2), Math.sin(time * 2), -Math.sin(time * 2), Math.cos(time * 2), 0, 100],
                            glData: {
                                vertex: rect_points4_byRect({ width: 40, height: 40 }),
                                rgba: [100, 100, 255, 255]
                            }
                        }
                    ]
                }
            ]
        };

        const stats = renderer.render(treeData);

        // 计算FPS
        if (frameCount % 60 === 0) {
            const now = performance.now();
            const fps = 60000 / (now - lastFpsTime);
            lastFpsTime = now;

            updatePerformanceInfo(`
                动画渲染中...<br>
                FPS: ${fps.toFixed(1)}<br>
                总节点数: ${stats.totalNodes}<br>
                可见节点数: ${stats.visibleNodes}<br>
                视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}
            `);
        }

        animationId = requestAnimationFrame(animate);
    }

    animate();
};

// 创建大数据量示例
window.renderMassiveTree = function () {
    initRenderer();

    updatePerformanceInfo('正在生成大数据量节点...');

    // 使用setTimeout让UI有时间更新
    setTimeout(() => {
        const startTime = performance.now();

        // 生成10万个节点的网格结构
        const TARGET_NODES = 100000;
        const colors = [
            [255, 100, 100, 255],
            [100, 255, 100, 255],
            [100, 100, 255, 255],
            [255, 255, 100, 255],
            [255, 100, 255, 255],
            [100, 255, 255, 255],
            [200, 200, 200, 255],
            [150, 200, 100, 255]
        ];

        let nodeCount = 0;
        const nodes = [];
        const gridSize = Math.ceil(Math.sqrt(TARGET_NODES));
        const nodeSize = Math.max(2, Math.floor(1000 / gridSize)); // 根据画布大小调整节点大小
        const spacing = nodeSize + 1;

        for (let i = 0; i < gridSize && nodeCount < TARGET_NODES; i++) {
            for (let j = 0; j < gridSize && nodeCount < TARGET_NODES; j++) {
                nodes.push({
                    id: `grid_node_${nodeCount}`,
                    mat: [1, 0, 0, 1, j * spacing, i * spacing],
                    glData: {
                        vertex: rect_points4_byRect({ width: nodeSize, height: nodeSize }),
                        rgba: colors[(i + j) % colors.length]
                    }
                });
                nodeCount++;
            }
        }

        const treeData = {
            id: 'massive_root',
            mat: [1, 0, 0, 1, 0, 0],
            children: nodes
        };

        const dataGenTime = performance.now() - startTime;
        updatePerformanceInfo(`数据生成完成: ${nodeCount}个节点, 耗时: ${dataGenTime.toFixed(2)}ms`);

        // 渲染
        const renderStartTime = performance.now();
        const stats = renderer.render(treeData);
        const renderTime = performance.now() - renderStartTime;

        const totalTime = performance.now() - startTime;

        updatePerformanceInfo(`
            节点数量: ${nodeCount.toLocaleString()}<br>
            数据生成: ${dataGenTime.toFixed(2)}ms<br>
            渲染时间: ${renderTime.toFixed(2)}ms<br>
            总耗时: ${totalTime.toFixed(2)}ms<br>
            渲染性能: ${(nodeCount / renderTime * 1000).toFixed(0)} 节点/秒<br>
            总节点数: ${stats.totalNodes}<br>
            可见节点数: ${stats.visibleNodes}<br>
            裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%<br>
            <span style="color: green;">✓ 批量渲染：单次drawElements调用</span>
        `);

    }, 10);
};

// 创建视口裁剪演示
window.renderViewportCulling = function () {
    initRenderer();

    // 创建大范围分布的节点
    const nodes = [];
    const colors = [
        [255, 100, 100, 255],
        [100, 255, 100, 255],
        [100, 100, 255, 255],
        [255, 255, 100, 255]
    ];

    // 在更大的范围内创建节点
    for (let i = 0; i < 50; i++) {
        for (let j = 0; j < 50; j++) {
            nodes.push({
                id: `culling_node_${i}_${j}`,
                mat: [1, 0, 0, 1, j * 100, i * 100],
                glData: {
                    vertex: rect_points4_byRect({ width: 80, height: 80 }),
                    rgba: colors[(i + j) % colors.length]
                }
            });
        }
    }

    const treeData = {
        id: 'culling_root',
        mat: [0.1, 0, 0, 0.1, 0, 0], // 缩小到0.1倍，这样大部分节点在视口外
        children: nodes
    };

    const stats = renderer.render(treeData);

    updatePerformanceInfo(`
        视口裁剪演示<br>
        总节点数: ${stats.totalNodes}<br>
        可见节点数: ${stats.visibleNodes}<br>
        裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%<br>
        视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}<br>
        缩放: ${stats.viewport.scale.toFixed(3)}x<br>
        <span style="color: blue;">💡 尝试滚轮缩放查看裁剪效果</span>
    `);
};

// 创建性能测试动画
window.renderMassiveAnimatedTree = function () {
    initRenderer();

    if (animationId) {
        cancelAnimationFrame(animationId);
    }

    const TARGET_NODES = 50000; // 动画时减少节点数量以保持流畅
    let frameCount = 0;
    let lastFpsTime = performance.now();

    // 生成静态节点数据
    const nodes = [];
    const gridSize = Math.ceil(Math.sqrt(TARGET_NODES));
    const nodeSize = Math.max(1, Math.floor(600 / gridSize));
    const spacing = nodeSize + 1;

    for (let i = 0; i < gridSize && nodes.length < TARGET_NODES; i++) {
        for (let j = 0; j < gridSize && nodes.length < TARGET_NODES; j++) {
            nodes.push({
                baseX: j * spacing,
                baseY: i * spacing,
                size: nodeSize,
                colorIndex: (i + j) % 6,
                id: `anim_node_${nodes.length}`
            });
        }
    }

    const colors = [
        [255, 100, 100, 255],
        [100, 255, 100, 255],
        [100, 100, 255, 255],
        [255, 255, 100, 255],
        [255, 100, 255, 255],
        [100, 255, 255, 255]
    ];

    function animate() {
        const time = performance.now() * 0.001;
        frameCount++;

        // 创建动态树形数据
        const animatedNodes = nodes.map((node, index) => {
            const wave = Math.sin(time + index * 0.001) * 5;
            return {
                id: node.id,
                mat: [1, 0, 0, 1, node.baseX + wave, node.baseY],
                glData: {
                    vertex: rect_points4_byRect({ width: node.size, height: node.size }),
                    rgba: colors[node.colorIndex]
                }
            };
        });

        const treeData = {
            id: 'massive_anim_root',
            mat: [1, 0, 0, 1, 0, 0],
            children: animatedNodes
        };

        const renderStart = performance.now();
        const stats = renderer.render(treeData);
        const renderTime = performance.now() - renderStart;

        // 计算FPS
        if (frameCount % 60 === 0) {
            const now = performance.now();
            const fps = 60000 / (now - lastFpsTime);
            lastFpsTime = now;

            updatePerformanceInfo(`
                大数据量动画渲染<br>
                动画节点: ${nodes.length.toLocaleString()}<br>
                FPS: ${fps.toFixed(1)}<br>
                渲染时间: ${renderTime.toFixed(2)}ms<br>
                性能: ${(nodes.length / renderTime * 1000).toFixed(0)} 节点/秒<br>
                可见节点: ${stats.visibleNodes}<br>
                裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%
            `);
        }

        animationId = requestAnimationFrame(animate);
    }

    animate();
};

// 添加滚轮缩放功能
function addWheelZoomSupport(treeData) {
    const canvas = document.getElementById('canvas');

    function handleWheel(event) {
        event.preventDefault();

        if (!treeData.mat) {
            treeData.mat = [1, 0, 0, 1, 0, 0];
        }

        const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;

        // 应用缩放
        treeData.mat[0] *= scaleFactor;
        treeData.mat[3] *= scaleFactor;

        // 重新渲染
        const stats = renderer.render(treeData);

        updatePerformanceInfo(`
            视口裁剪演示 (支持滚轮缩放)<br>
            总节点数: ${stats.totalNodes}<br>
            可见节点数: ${stats.visibleNodes}<br>
            裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%<br>
            视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}<br>
            缩放: ${stats.viewport.scale.toFixed(3)}x<br>
            <span style="color: blue;">💡 继续滚轮缩放查看裁剪效果</span>
        `);
    }

    canvas.addEventListener('wheel', handleWheel);

    // 返回清理函数
    return () => {
        canvas.removeEventListener('wheel', handleWheel);
    };
}

// 修改视口裁剪演示以支持滚轮缩放
window.renderViewportCulling = function () {
    initRenderer();

    // 清理之前的事件监听器
    if (window.currentZoomCleanup) {
        window.currentZoomCleanup();
    }

    // 创建大范围分布的节点
    const nodes = [];
    const colors = [
        [255, 100, 100, 255],
        [100, 255, 100, 255],
        [100, 100, 255, 255],
        [255, 255, 100, 255]
    ];

    // 在更大的范围内创建节点
    for (let i = 0; i < 50; i++) {
        for (let j = 0; j < 50; j++) {
            nodes.push({
                id: `culling_node_${i}_${j}`,
                mat: [1, 0, 0, 1, j * 100, i * 100],
                glData: {
                    vertex: rect_points4_byRect({ width: 80, height: 80 }),
                    rgba: colors[(i + j) % colors.length]
                }
            });
        }
    }

    const treeData = {
        id: 'culling_root',
        mat: [0.1, 0, 0, 0.1, 0, 0], // 缩小到0.1倍，这样大部分节点在视口外
        children: nodes
    };

    const stats = renderer.render(treeData);

    updatePerformanceInfo(`
        视口裁剪演示 (支持滚轮缩放)<br>
        总节点数: ${stats.totalNodes}<br>
        可见节点数: ${stats.visibleNodes}<br>
        裁剪效率: ${((stats.visibleNodes / stats.totalNodes) * 100).toFixed(1)}%<br>
        视口: ${stats.viewport.width.toFixed(0)} x ${stats.viewport.height.toFixed(0)}<br>
        缩放: ${stats.viewport.scale.toFixed(3)}x<br>
        <span style="color: blue;">💡 使用滚轮缩放查看裁剪效果</span>
    `);

    // 添加滚轮缩放支持
    window.currentZoomCleanup = addWheelZoomSupport(treeData);
};

renderMassiveTree();


export function callAA(){}
interface Rect {
    x?: number;
    y?: number;
    width: number;
    height: number;
}
type Matrix = number[];
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[];//int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number
    uvs?: number[]
}
interface GlobalStyles {
    id?: string;  // 节点唯一标识
    mat?: Matrix;
    alpha?: number;
}

type DisplayObject = GlobalStyles & (SingleDisplay | ComposeDisplay);
interface SingleDisplay {
    glData: GLData;
}

interface ComposeDisplay {
    children?: DisplayObject[];
}
import {
    BufferData,
    GLBufferManager,
    ViewportInfo,
    buildSpatialIndex,
    collectAllRenderData,
    createGLBufferManager,
    createScreenMatrix,
    generateViewportIndices,
    getViewportFromRoot
} from './gl_buffer';

// 渲染统计信息
export interface RenderStats {
    totalNodes: number;
    visibleNodes: number;
    viewport: ViewportInfo;
}

// 渲染器接口
export interface Renderer {
    el: HTMLCanvasElement;
    render(tree: DisplayObject, viewport?: ViewportInfo): RenderStats;
}

// 创建WebGL渲染器
export function createGlRenderer(canvas: HTMLCanvasElement): Renderer {
    const gl = canvas.getContext('webgl2') as WebGL2RenderingContext;
    if (!gl) {
        throw new Error('WebGL2 not supported');
    }

    // 顶点着色器
    const vertexShaderSource = `#version 300 es
        in vec2 a_position;
        in vec4 a_color;
        uniform mat3 u_screenMatrix;
        out vec4 v_color;

        void main() {
            vec3 position = u_screenMatrix * vec3(a_position, 1.0);
            gl_Position = vec4(position.xy, 0.0, 1.0);
            v_color = a_color;
        }
    `;

    // 片段着色器
    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec4 v_color;
        out vec4 fragColor;

        void main() {
            fragColor = v_color;
        }
    `;

    // 创建着色器
    function createShader(type: number, source: string): WebGLShader {
        const shader = gl.createShader(type)!;
        gl.shaderSource(shader, source);
        gl.compileShader(shader);

        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const error = gl.getShaderInfoLog(shader);
            gl.deleteShader(shader);
            throw new Error(`Shader compilation error: ${error}`);
        }

        return shader;
    }

    // 创建程序
    function createProgram(vertexShader: WebGLShader, fragmentShader: WebGLShader): WebGLProgram {
        const program = gl.createProgram()!;
        gl.attachShader(program, vertexShader);
        gl.attachShader(program, fragmentShader);
        gl.linkProgram(program);

        if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
            const error = gl.getProgramInfoLog(program);
            gl.deleteProgram(program);
            throw new Error(`Program linking error: ${error}`);
        }

        return program;
    }

    // 初始化着色器程序
    const vertexShader = createShader(gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = createProgram(vertexShader, fragmentShader);

    // 获取属性和uniform位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');
    const screenMatrixLocation = gl.getUniformLocation(program, 'u_screenMatrix');

    // 创建缓冲区
    const positionBuffer = gl.createBuffer();
    const colorBuffer = gl.createBuffer();
    const indexBuffer = gl.createBuffer();

    // 启用扩展名webgl2不需要
    gl.getExtension('OES_element_index_uint');

    // 渲染函数
    function performRender(bufferData: BufferData, indices: Uint32Array, screenMatrix: number[]) {
        gl.viewport(0, 0, canvas.width, canvas.height);
        gl.clearColor(0.9, 0.9, 0.9, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        gl.useProgram(program);

        // 设置屏幕矩阵
        gl.uniformMatrix3fv(screenMatrixLocation, false, screenMatrix);

        // 绑定位置数据
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, bufferData.positions.subarray(0, bufferData.positionCount), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

        // 绑定颜色数据
        gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, bufferData.colors.subarray(0, bufferData.colorCount), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);

        // 绑定索引数据
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, indices, gl.STATIC_DRAW);

        // 执行绘制
        if (indices.length > 0) {
            gl.drawElements(gl.TRIANGLES, indices.length, gl.UNSIGNED_INT, 0);
        }
    }

    return {
        el: canvas,

        render(tree: DisplayObject, viewport?: ViewportInfo): RenderStats {
            // 使用类型断言来处理__gl_buffer属性
            const rootWithBuffer = tree as any;

            // 获取或创建root的私有buffer数据
            if (!rootWithBuffer.__gl_buffer) {
                rootWithBuffer.__gl_buffer = createGLBufferManager(canvas.width, canvas.height);
            }

            const rootMatrix = tree.mat || [1, 0, 0, 1, 0, 0];
            const manager = rootWithBuffer.__gl_buffer as GLBufferManager;

            // 如果没有提供viewport，从root的mat和画布尺寸计算
            const currentViewport = viewport || getViewportFromRoot(rootMatrix, canvas.width, canvas.height);

            // 检查是否需要重建数据
            let now = performance.now();
            if (manager.needsRebuild || !manager.spatialIndex || !manager.bufferData ||
                now - manager.lastUpdateTime > 1000) { // 1秒重建一次

                // 收集所有节点的渲染数据到buffer（世界坐标）
                collectAllRenderData(tree, manager, [1, 0, 0, 1, 0, 0]);

                // 构建空间索引
                manager.spatialIndex = buildSpatialIndex(tree, manager, [1, 0, 0, 1, 0, 0]);

                manager.lastUpdateTime = now;
                manager.needsRebuild = false;
            }
            console.log('buffer', performance.now() - now | 0);
            now = performance.now();

            // 生成屏幕矩阵
            const screenMatrix = createScreenMatrix(currentViewport);

            // 生成视口内的动态索引
            const visibleIndices = generateViewportIndices(manager, currentViewport);
            console.log('visibleIndices', performance.now() - now | 0);
            now = performance.now();

            // 执行渲染
            if (manager.bufferData) {
                performRender(manager.bufferData, visibleIndices, screenMatrix);
            }
            console.log('performRender', performance.now() - now | 0);
            now = performance.now();

            // 计算总节点数
            let totalNodes = 0;
            function countNodes(node: DisplayObject) {
                if ('glData' in node) totalNodes++;
                if ('children' in node && node.children) {
                    node.children.forEach(countNodes);
                }
            }
            countNodes(tree);

            // 返回渲染统计信息
            return {
                totalNodes,
                visibleNodes: visibleIndices.length / 6, // 每个节点6个索引
                viewport: currentViewport
            };
        }
    };
}

// 导出一些辅助函数用于演示
export function createTestNode(mat: Matrix, color = [0, 0, 0, 255], id?: string) {
    return {
        id,
        mat,
        glData: {
            vertex: [-50, -50, 50, -50, 50, 50, -50, 50],
            rgba: color
        }
    };
}

export function rect_points4_byRect(rect: { width: number; height: number }) {
    const { width, height } = rect;
    return [-width/2, -height/2, width/2, -height/2, width/2, height/2, -width/2, height/2];
}
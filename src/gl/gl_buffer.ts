import { BoundingBox, QuadTree, createQuadTree } from '../quad_tree';

// 节点数据类型
export enum NodeDataType {
    FIXED = 'fixed',    // 固定长度数据（如矩形）
    DYNAMIC = 'dynamic' // 动态长度数据（如多边形）
}

// 视口信息
export interface ViewportInfo {
    x: number;
    y: number;
    width: number;
    height: number;
    scale: number;
}

// Buffer数据结构
export interface BufferData {
    positions: Float32Array;
    colors: Float32Array;
    positionCount: number;
    colorCount: number;
    // 双向分配管理
    fixedPtr: number;      // 固定数据从前向后分配
    dynamicPtr: number;    // 动态数据从后向前分配
    freeFixedSlots: { offset: number; count: number }[];
    freeDynamicSlots: { offset: number; count: number }[];
}

// 节点缓存信息
export interface NodeCache {
    id: string;
    node: DisplayObject;
    dataType: NodeDataType;
    vertexOffset: number;
    vertexCount: number;
    parentMatrix: number[];
    bounds: BoundingBox;
}

// GL Buffer管理器
export interface GLBufferManager {
    spatialIndex: QuadTree | null;
    nodeCache: Map<string, NodeCache>;
    bufferData: BufferData | null;
    lastUpdateTime: number;
    needsRebuild: boolean;
    canvasWidth: number;
    canvasHeight: number;
}

// 创建buffer数据
export function createBufferData(estimatedNodes: number): BufferData {
    const totalVertices = estimatedNodes * 4; // 假设每个节点最多4个顶点
    
    return {
        positions: new Float32Array(totalVertices * 2),
        colors: new Float32Array(totalVertices * 4),
        positionCount: 0,
        colorCount: 0,
        fixedPtr: 0,
        dynamicPtr: totalVertices,
        freeFixedSlots: [],
        freeDynamicSlots: []
    };
}

// 创建GL Buffer管理器
export function createGLBufferManager(canvasWidth: number, canvasHeight: number): GLBufferManager {
    return {
        spatialIndex: null,
        nodeCache: new Map(),
        bufferData: null,
        lastUpdateTime: 0,
        needsRebuild: true,
        canvasWidth,
        canvasHeight
    };
}

// 根据root的mat和画布尺寸计算视口信息
export function getViewportFromRoot(rootMat: number[], canvasWidth: number, canvasHeight: number): ViewportInfo {
    const scaleX = rootMat[0];
    const scaleY = rootMat[3];
    const translateX = rootMat[4];
    const translateY = rootMat[5];
    
    return {
        x: -translateX / scaleX,
        y: -translateY / scaleY,
        width: canvasWidth / scaleX,
        height: canvasHeight / scaleY,
        scale: scaleX
    };
}

// 从视口信息生成WebGL屏幕矩阵
export function createScreenMatrix(viewport: ViewportInfo): number[] {
    const scaleX = 2 / viewport.width;
    const scaleY = -2 / viewport.height;
    const translateX = -1 - viewport.x * scaleX;
    const translateY = 1 - viewport.y * scaleY;
    
    return [
        scaleX, 0, 0,
        0, scaleY, 0,
        translateX, translateY, 1
    ];
}

// 矩阵乘法
function multiplyMatrix(a: number[], b: number[]): number[] {
    return [
        a[0] * b[0] + a[2] * b[1],
        a[1] * b[0] + a[3] * b[1],
        a[0] * b[2] + a[2] * b[3],
        a[1] * b[2] + a[3] * b[3],
        a[0] * b[4] + a[2] * b[5] + a[4],
        a[1] * b[4] + a[3] * b[5] + a[5]
    ];
}

// 计算节点的世界坐标包围盒
export function getNodeWorldBounds(node: DisplayObject, parentMatrix: number[]): BoundingBox | null {
    if (!('glData' in node)) return null;
    
    const currentMatrix = node.mat ? multiplyMatrix(parentMatrix, node.mat) : parentMatrix;
    const { vertex } = node.glData;
    
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    for (let i = 0; i < vertex.length; i += 2) {
        const worldX = currentMatrix[0] * vertex[i] + currentMatrix[2] * vertex[i + 1] + currentMatrix[4];
        const worldY = currentMatrix[1] * vertex[i] + currentMatrix[3] * vertex[i + 1] + currentMatrix[5];
        
        minX = Math.min(minX, worldX);
        minY = Math.min(minY, worldY);
        maxX = Math.max(maxX, worldX);
        maxY = Math.max(maxY, worldY);
    }
    
    return { minX, minY, maxX, maxY };
}

// 生成节点ID
function generateNodeId(): string {
    return 'node_' + Math.random().toString(36).substring(2, 11);
}

// 分配顶点槽位（固定长度数据）
function allocateFixedSlot(bufferData: BufferData, vertexCount: number): number {
    // 优先复用空闲槽位
    for (let i = 0; i < bufferData.freeFixedSlots.length; i++) {
        const slot = bufferData.freeFixedSlots[i];
        if (slot.count >= vertexCount) {
            bufferData.freeFixedSlots.splice(i, 1);
            if (slot.count > vertexCount) {
                // 剩余空间重新加入空闲列表
                bufferData.freeFixedSlots.push({
                    offset: slot.offset + vertexCount,
                    count: slot.count - vertexCount
                });
            }
            return slot.offset;
        }
    }
    
    // 从前向后分配新槽位
    const newSlot = bufferData.fixedPtr;
    if (newSlot + vertexCount < bufferData.dynamicPtr) {
        bufferData.fixedPtr += vertexCount;
        return newSlot;
    }
    
    return -1; // 空间不足
}

// 分配顶点槽位（动态长度数据）
function allocateDynamicSlot(bufferData: BufferData, vertexCount: number): number {
    // 优先复用空闲槽位
    for (let i = 0; i < bufferData.freeDynamicSlots.length; i++) {
        const slot = bufferData.freeDynamicSlots[i];
        if (slot.count >= vertexCount) {
            bufferData.freeDynamicSlots.splice(i, 1);
            if (slot.count > vertexCount) {
                // 剩余空间重新加入空闲列表
                bufferData.freeDynamicSlots.push({
                    offset: slot.offset,
                    count: slot.count - vertexCount
                });
            }
            return slot.offset - vertexCount + 1;
        }
    }
    
    // 从后向前分配新槽位
    const newSlot = bufferData.dynamicPtr - vertexCount;
    if (newSlot > bufferData.fixedPtr) {
        bufferData.dynamicPtr = newSlot;
        return newSlot;
    }
    
    return -1; // 空间不足
}

// 释放顶点槽位
function freeVertexSlot(bufferData: BufferData, dataType: NodeDataType, offset: number, count: number) {
    if (dataType === NodeDataType.FIXED) {
        bufferData.freeFixedSlots.push({ offset, count });
    } else {
        bufferData.freeDynamicSlots.push({ offset, count });
    }

    // 清空数据
    const posStart = offset * 2;
    const colorStart = offset * 4;
    for (let i = 0; i < count; i++) {
        bufferData.positions[posStart + i * 2] = 0;
        bufferData.positions[posStart + i * 2 + 1] = 0;
        bufferData.colors[colorStart + i * 4] = 0;
        bufferData.colors[colorStart + i * 4 + 1] = 0;
        bufferData.colors[colorStart + i * 4 + 2] = 0;
        bufferData.colors[colorStart + i * 4 + 3] = 0;
    }
}

// 构建空间索引
export function buildSpatialIndex(tree: DisplayObject, manager: GLBufferManager, parentMatrix: number[] = [1, 0, 0, 1, 0, 0]): QuadTree {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    // 计算世界边界
    function calculateBounds(node: DisplayObject, matrix: number[]) {
        const bounds = getNodeWorldBounds(node, matrix);
        if (bounds) {
            minX = Math.min(minX, bounds.minX);
            minY = Math.min(minY, bounds.minY);
            maxX = Math.max(maxX, bounds.maxX);
            maxY = Math.max(maxY, bounds.maxY);
        }

        if ('children' in node && node.children) {
            const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;
            node.children.forEach(child => calculateBounds(child, currentMatrix));
        }
    }

    calculateBounds(tree, parentMatrix);

    // 扩展边界
    const padding = 100;
    const worldBounds: BoundingBox = {
        minX: minX - padding,
        minY: minY - padding,
        maxX: maxX + padding,
        maxY: maxY + padding
    };

    const spatialIndex = createQuadTree(worldBounds, 10, 6);

    // 插入节点到四叉树
    function insertNodes(node: DisplayObject, matrix: number[]) {
        const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;

        // 只处理有glData和ID的节点
        if ('glData' in node && node.id) {
            const bounds = getNodeWorldBounds(node, matrix);
            if (bounds) {
                spatialIndex.insert({ id: node.id, bounds });
            }
        }

        if ('children' in node && node.children) {
            node.children.forEach(child => insertNodes(child, currentMatrix));
        }
    }

    insertNodes(tree, parentMatrix);
    return spatialIndex;
}

// 收集所有节点的渲染数据到buffer（世界坐标）
export function collectAllRenderData(tree: DisplayObject, manager: GLBufferManager, parentMatrix: number[] = [1, 0, 0, 1, 0, 0]) {
    if (!manager.bufferData) {
        manager.bufferData = createBufferData(100000);
    }

    // 重置buffer数据
    const bufferData = manager.bufferData;
    bufferData.positionCount = 0;
    bufferData.colorCount = 0;

    // 清空缓存
    manager.nodeCache.clear();

    // 递归收集节点数据
    function collectNodeData(node: DisplayObject, matrix: number[]) {
        const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;
        const nodeId = node.id || generateNodeId();

        // 确保节点有ID
        if (!node.id) {
            node.id = nodeId;
        }

        if ('glData' in node) {
            const { vertex, rgba = [255, 255, 255, 255] } = node.glData;
            const vertexCount = vertex.length / 2;

            // 判断数据类型（简化：4个顶点为固定，其他为动态）
            const dataType = vertexCount === 4 ? NodeDataType.FIXED : NodeDataType.DYNAMIC;

            // 分配槽位
            const vertexOffset = dataType === NodeDataType.FIXED
                ? allocateFixedSlot(bufferData, vertexCount)
                : allocateDynamicSlot(bufferData, vertexCount);

            if (vertexOffset !== -1) {
                // 计算世界坐标顶点位置
                for (let i = 0; i < vertex.length; i += 2) {
                    const worldX = currentMatrix[0] * vertex[i] + currentMatrix[2] * vertex[i + 1] + currentMatrix[4];
                    const worldY = currentMatrix[1] * vertex[i] + currentMatrix[3] * vertex[i + 1] + currentMatrix[5];

                    const posIndex = (vertexOffset + i / 2) * 2;
                    const colorIndex = (vertexOffset + i / 2) * 4;

                    bufferData.positions[posIndex] = worldX;
                    bufferData.positions[posIndex + 1] = worldY;

                    bufferData.colors[colorIndex] = rgba[0] / 255;
                    bufferData.colors[colorIndex + 1] = rgba[1] / 255;
                    bufferData.colors[colorIndex + 2] = rgba[2] / 255;
                    bufferData.colors[colorIndex + 3] = rgba[3] / 255;
                }

                bufferData.positionCount = Math.max(bufferData.positionCount, (vertexOffset + vertexCount) * 2);
                bufferData.colorCount = Math.max(bufferData.colorCount, (vertexOffset + vertexCount) * 4);

                // 计算包围盒
                const bounds = getNodeWorldBounds(node, matrix);
                if (bounds) {
                    // 更新节点缓存
                    manager.nodeCache.set(nodeId, {
                        id: nodeId,
                        node: node,
                        dataType,
                        vertexOffset,
                        vertexCount,
                        parentMatrix: [...currentMatrix],
                        bounds
                    });
                }
            }
        }

        if ('children' in node && node.children) {
            node.children.forEach(child => collectNodeData(child, currentMatrix));
        }
    }

    collectNodeData(tree, parentMatrix);
}

// 根据视口生成动态索引数据
export function generateViewportIndices(manager: GLBufferManager, viewport: ViewportInfo): Uint32Array {
    if (!manager.spatialIndex || !manager.bufferData) {
        return new Uint32Array(0);
    }

    // 查询视口内的可见节点
    const viewportBounds: BoundingBox = {
        minX: viewport.x,
        minY: viewport.y,
        maxX: viewport.x + viewport.width,
        maxY: viewport.y + viewport.height
    };

    const visibleNodes = manager.spatialIndex.query(viewportBounds);
    const visibleNodeIds = new Set(visibleNodes.map(n => n.id));

    const indices: number[] = [];

    // 收集可见节点的索引
    for (const [nodeId, cached] of manager.nodeCache) {
        if (visibleNodeIds.has(nodeId)) {
            const startVertex = cached.vertexOffset;

            // 生成矩形索引（假设都是矩形）
            if (cached.vertexCount === 4) {
                indices.push(
                    startVertex, startVertex + 1, startVertex + 2,
                    startVertex + 2, startVertex, startVertex + 3
                );
            } else {
                // 对于其他形状，生成三角扇形索引
                for (let i = 1; i < cached.vertexCount - 1; i++) {
                    indices.push(startVertex, startVertex + i, startVertex + i + 1);
                }
            }
        }
    }

    return new Uint32Array(indices);
}

interface CommonStyles {
    alpha?: number;
    clip?: { x: number; y: number }[];
    matrix?: number[];
    shadow?: {
        x: number;
        y: number;
        blur: number;
        color?: string;
    };
}
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[]; //int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number;
    uvs?: number[];
}
type LeafNode = CommonStyles & { glData: GLData }
type ContainerNode = CommonStyles & { children: Node[] }
type Node = LeafNode | ContainerNode;

// 简化的矩阵工具
const mat4 = {
    create(): number[] {
        return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];
    },
    multiply(out: number[], a: number[], b: number[]): number[] {
        // 完整的4x4矩阵乘法，但重点关注2D变换部分
        const a00 = a[0] || 0, a01 = a[1] || 0, a04 = a[4] || 0, a05 = a[5] || 0;
        const a12 = a[12] || 0, a13 = a[13] || 0;
        const b00 = b[0] || 0, b01 = b[1] || 0, b04 = b[4] || 0, b05 = b[5] || 0;
        const b12 = b[12] || 0, b13 = b[13] || 0;

        // 初始化输出矩阵为单位矩阵
        for (let i = 0; i < 16; i++) {
            out[i] = 0;
        }
        out[0] = out[5] = out[10] = out[15] = 1;

        // 2D变换部分
        out[0] = a00 * b00 + a01 * b04;
        out[1] = a00 * b01 + a01 * b05;
        out[4] = a04 * b00 + a05 * b04;
        out[5] = a04 * b01 + a05 * b05;
        out[12] = a00 * b12 + a01 * b13 + a12;
        out[13] = a04 * b12 + a05 * b13 + a13;

        return out;
    },
    ortho(out: number[], left: number, right: number, bottom: number, top: number): number[] {
        // 初始化为单位矩阵
        for (let i = 0; i < 16; i++) {
            out[i] = 0;
        }
        out[10] = out[15] = 1;

        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        out[0] = -2 * lr;
        out[5] = -2 * bt;
        out[12] = (left + right) * lr;
        out[13] = (top + bottom) * bt;
        return out;
    }
};

export function createGlRenderer() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') as WebGL2RenderingContext;

    if (!gl) {
        throw new Error('WebGL2 is not supported in this browser');
    }

    // 顶点着色器
    const vertexShaderSource = `#version 300 es
        in vec2 a_position;
        in vec4 a_color;
        in vec2 a_uv;
        in vec2 a_world_pos;
        uniform mat4 u_matrix;
        uniform mat4 u_projection;
        out vec4 v_color;
        out vec2 v_uv;
        out vec2 v_world_pos;

        void main() {
            gl_Position = u_projection * u_matrix * vec4(a_position, 0.0, 1.0);
            v_color = a_color;
            v_uv = a_uv;
            v_world_pos = a_world_pos;
        }
    `;

    // 片段着色器
    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec4 v_color;
        in vec2 v_uv;
        in vec2 v_world_pos;
        uniform sampler2D u_texture;
        uniform bool u_use_texture;
        uniform bool u_use_clip;
        uniform vec2 u_clip_points[16]; // 最多支持16个裁剪点
        uniform int u_clip_count;
        uniform bool u_is_shadow;
        uniform float u_shadow_blur;
        uniform vec2 u_shadow_center;
        out vec4 fragColor;

        // 点在多边形内部检测
        bool pointInPolygon(vec2 point, vec2 polygon[16], int count) {
            bool inside = false;
            for (int i = 0, j = count - 1; i < count; j = i++) {
                if (((polygon[i].y > point.y) != (polygon[j].y > point.y)) &&
                    (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
                    inside = !inside;
                }
            }
            return inside;
        }

        void main() {
            // 裁剪检测
            if (u_use_clip && u_clip_count > 2) {
                if (!pointInPolygon(v_world_pos, u_clip_points, u_clip_count)) {
                    discard;
                }
            }

            vec4 color = v_color;

            // 阴影模糊效果
            if (u_is_shadow && u_shadow_blur > 0.0) {
                float distance = length(v_world_pos - u_shadow_center);
                float blur_factor = 1.0 - smoothstep(0.0, u_shadow_blur, distance);
                color.a *= blur_factor;

                // 添加柔和的边缘
                float edge_softness = u_shadow_blur * 0.5;
                float edge_factor = 1.0 - smoothstep(u_shadow_blur - edge_softness, u_shadow_blur, distance);
                color.a *= edge_factor;
            }

            if (u_use_texture) {
                fragColor = texture(u_texture, v_uv) * color;
            } else {
                fragColor = color;
            }
        }
    `;

    // 创建着色器
    function createShader(type: number, source: string): WebGLShader {
        const shader = gl.createShader(type)!;
        gl.shaderSource(shader, source);
        gl.compileShader(shader);

        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const error = gl.getShaderInfoLog(shader);
            gl.deleteShader(shader);
            throw new Error(`Shader compilation error: ${error}`);
        }

        return shader;
    }

    // 创建程序
    const vertexShader = createShader(gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const error = gl.getProgramInfoLog(program);
        throw new Error(`Program linking error: ${error}`);
    }

    // 获取属性和uniform位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');
    const uvLocation = gl.getAttribLocation(program, 'a_uv');
    const worldPosLocation = gl.getAttribLocation(program, 'a_world_pos');
    const matrixLocation = gl.getUniformLocation(program, 'u_matrix');
    const projectionLocation = gl.getUniformLocation(program, 'u_projection');
    const useTextureLocation = gl.getUniformLocation(program, 'u_use_texture');
    const useClipLocation = gl.getUniformLocation(program, 'u_use_clip');
    const clipPointsLocation = gl.getUniformLocation(program, 'u_clip_points');
    const clipCountLocation = gl.getUniformLocation(program, 'u_clip_count');
    const isShadowLocation = gl.getUniformLocation(program, 'u_is_shadow');
    const shadowBlurLocation = gl.getUniformLocation(program, 'u_shadow_blur');
    const shadowCenterLocation = gl.getUniformLocation(program, 'u_shadow_center');

    // 创建缓冲区
    const positionBuffer = gl.createBuffer();
    const colorBuffer = gl.createBuffer();
    const uvBuffer = gl.createBuffer();
    const worldPosBuffer = gl.createBuffer();
    const indexBuffer = gl.createBuffer();

    // 设置WebGL状态
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    // 缓存
    const bufferCache = new Map<string, { positions: number[], colors: number[], uvs: number[], indices: number[] }>();
    const textureCache = new Map<number, WebGLTexture>();

    return render;

    function render(node: Node, { width, height }: { width: number; height: number }) {
        canvas.width = width;
        canvas.height = height;

        // 设置视口
        gl.viewport(0, 0, width, height);
        gl.clearColor(1,1,1, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        gl.useProgram(program);

        // 设置投影矩阵
        const projectionMatrix = mat4.create();
        mat4.ortho(projectionMatrix, 0, width, height, 0);
        gl.uniformMatrix4fv(projectionLocation, false, projectionMatrix);

        // 分别收集不同类型的渲染数据
        const renderGroups = collectRenderGroups(node, mat4.create());

        // 先渲染所有阴影（确保阴影在底层）
        for (const group of renderGroups) {
            if (group.isShadow && group.data.positions.length > 0) {
                renderPass(group.data, group.clipInfo, group.isShadow);
            }
        }

        // 再渲染所有主要内容（在阴影之上）
        for (const group of renderGroups) {
            if (!group.isShadow && group.data.positions.length > 0) {
                renderPass(group.data, group.clipInfo, group.isShadow);
            }
        }
        return canvas;
    }

    // 渲染通道函数
    function renderPass(renderData: any, clipInfo: any, isShadow: boolean) {
        // 位置数据
        gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.positions), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(positionLocation);
        gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

        // 颜色数据
        gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.colors), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(colorLocation);
        gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);

        // UV数据
        gl.bindBuffer(gl.ARRAY_BUFFER, uvBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.uvs), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(uvLocation);
        gl.vertexAttribPointer(uvLocation, 2, gl.FLOAT, false, 0, 0);

        // 世界坐标数据
        gl.bindBuffer(gl.ARRAY_BUFFER, worldPosBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.worldPositions), gl.STATIC_DRAW);
        gl.enableVertexAttribArray(worldPosLocation);
        gl.vertexAttribPointer(worldPosLocation, 2, gl.FLOAT, false, 0, 0);

        // 索引数据
        gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
        gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(renderData.indices), gl.STATIC_DRAW);

        // 设置uniform
        gl.uniformMatrix4fv(matrixLocation, false, mat4.create());
        gl.uniform1i(useTextureLocation, 0);

        // 设置阴影相关uniform
        gl.uniform1i(isShadowLocation, isShadow ? 1 : 0);
        if (isShadow && renderData.shadowBlur !== undefined) {
            gl.uniform1f(shadowBlurLocation, renderData.shadowBlur);
            gl.uniform2f(shadowCenterLocation, renderData.shadowCenter[0], renderData.shadowCenter[1]);
        } else {
            gl.uniform1f(shadowBlurLocation, 0.0);
            gl.uniform2f(shadowCenterLocation, 0.0, 0.0);
        }

        // 设置裁剪
        if (clipInfo && clipInfo.points.length > 0) {
            gl.uniform1i(useClipLocation, 1);
            gl.uniform2fv(clipPointsLocation, clipInfo.points);
            gl.uniform1i(clipCountLocation, clipInfo.count);
        } else {
            gl.uniform1i(useClipLocation, 0);
        }

        // 绘制
        gl.drawElements(gl.TRIANGLES, renderData.indices.length, gl.UNSIGNED_SHORT, 0);
    }

    // 收集渲染组（按裁剪区域分组）
    function collectRenderGroups(node: Node, parentMatrix: number[]): Array<{
        data: { positions: number[], colors: number[], uvs: number[], worldPositions: number[], indices: number[] },
        clipInfo: { points: number[], count: number } | null,
        isShadow: boolean
    }> {
        const groups: Array<{
            data: { positions: number[], colors: number[], uvs: number[], worldPositions: number[], indices: number[] },
            clipInfo: { points: number[], count: number } | null,
            isShadow: boolean
        }> = [];

        // 收集不同裁剪区域的渲染数据
        collectNodeGroups(node, parentMatrix, null, groups);

        return groups;
    }

    // 递归收集节点组
    function collectNodeGroups(
        node: Node,
        parentMatrix: number[],
        currentClip: { points: number[], count: number } | null,
        groups: Array<{
            data: { positions: number[], colors: number[], uvs: number[], worldPositions: number[], indices: number[] },
            clipInfo: { points: number[], count: number } | null,
            isShadow: boolean
        }>
    ) {
        // 计算当前变换矩阵
        let currentMatrix = [...parentMatrix];

        if (node.matrix && node.matrix.length >= 6) {
            const nodeMatrix = mat4.create();
            nodeMatrix[0] = node.matrix[0] || 1;
            nodeMatrix[1] = node.matrix[1] || 0;
            nodeMatrix[4] = node.matrix[2] || 0;
            nodeMatrix[5] = node.matrix[3] || 1;
            nodeMatrix[12] = node.matrix[4] || 0;
            nodeMatrix[13] = node.matrix[5] || 0;

            currentMatrix = mat4.multiply(mat4.create(), parentMatrix, nodeMatrix);
        }

        // 检查是否有新的裁剪区域
        let nodeClip = currentClip;
        if (node.clip && node.clip.length >= 3) {
            const points: number[] = [];
            for (const point of node.clip) {
                points.push(point.x, point.y);
            }
            nodeClip = { points, count: node.clip.length };
        }

        // 处理叶子节点
        if ('glData' in node) {
            // 先渲染阴影（如果有）
            if (node.shadow) {
                let shadowGroup = groups.find(g => g.isShadow && g.clipInfo === nodeClip);
                if (!shadowGroup) {
                    shadowGroup = {
                        data: { positions: [], colors: [], uvs: [], worldPositions: [], indices: [] },
                        clipInfo: nodeClip,
                        isShadow: true
                    };
                    groups.push(shadowGroup);
                }
                addShadowToGroup(node, currentMatrix, shadowGroup);
            }

            // 渲染主要内容
            let mainGroup = groups.find(g => !g.isShadow && g.clipInfo === nodeClip);
            if (!mainGroup) {
                mainGroup = {
                    data: { positions: [], colors: [], uvs: [], worldPositions: [], indices: [] },
                    clipInfo: nodeClip,
                    isShadow: false
                };
                groups.push(mainGroup);
            }
            addNodeToGroup(node, currentMatrix, mainGroup);
        }

        // 处理子节点
        if ('children' in node && node.children) {
            for (const child of node.children) {
                collectNodeGroups(child, currentMatrix, nodeClip, groups);
            }
        }
    }

    // 添加节点到渲染组
    function addNodeToGroup(node: LeafNode, matrix: number[], group: any) {
        const { vertex, indices, rgba = [255, 255, 255, 255], tex } = node.glData;
        const alpha = node.alpha || 1;
        const startVertex = group.data.positions.length / 2;

        if (!vertex || vertex.length === 0) return;

        for (let i = 0; i < vertex.length; i += 2) {
            const x = vertex[i];
            const y = vertex[i + 1];

            if (!isNaN(x) && !isNaN(y)) {
                const transformedX = matrix[0] * x + matrix[4] * y + matrix[12];
                const transformedY = matrix[1] * x + matrix[5] * y + matrix[13];

                if (!isNaN(transformedX) && !isNaN(transformedY)) {
                    group.data.positions.push(transformedX, transformedY);
                    group.data.worldPositions.push(transformedX, transformedY);

                    group.data.colors.push(
                        rgba[0] / 255,
                        rgba[1] / 255,
                        rgba[2] / 255,
                        (rgba[3] / 255) * alpha
                    );

                    if (tex && tex.uvs && tex.uvs.length > i + 1) {
                        group.data.uvs.push(tex.uvs[i], tex.uvs[i + 1]);
                    } else {
                        group.data.uvs.push(0, 0);
                    }
                }
            }
        }

        // 添加索引
        const vertexCount = vertex.length / 2;
        if (indices && indices.length > 0) {
            for (const index of indices) {
                group.data.indices.push(startVertex + index);
            }
        } else {
            if (vertexCount === 4) {
                group.data.indices.push(
                    startVertex, startVertex + 1, startVertex + 2,
                    startVertex + 2, startVertex + 3, startVertex
                );
            } else {
                for (let i = 1; i < vertexCount - 1; i++) {
                    group.data.indices.push(startVertex, startVertex + i, startVertex + i + 1);
                }
            }
        }
    }

    // 添加阴影到渲染组
    function addShadowToGroup(node: LeafNode, matrix: number[], group: any) {
        if (!node.shadow) return;

        const { vertex, indices } = node.glData;
        const { x: shadowX, y: shadowY, blur = 0, color } = node.shadow;
        const alpha = node.alpha || 1;
        const startVertex = group.data.positions.length / 2;

        const shadowColor = color ? hexToRgba(color) : [0, 0, 0, 0.5];

        // 计算阴影中心点（用于模糊计算）
        let centerX = 0, centerY = 0;
        for (let i = 0; i < vertex.length; i += 2) {
            centerX += vertex[i];
            centerY += vertex[i + 1];
        }
        centerX = (centerX / (vertex.length / 2)) * matrix[0] + matrix[12] + shadowX;
        centerY = (centerY / (vertex.length / 2)) * matrix[5] + matrix[13] + shadowY;

        // 存储阴影信息到渲染数据
        group.data.shadowBlur = blur;
        group.data.shadowCenter = [centerX, centerY];

        if (vertex && vertex.length > 0) {
            const shadowMatrix = [...matrix];
            shadowMatrix[12] += shadowX;
            shadowMatrix[13] += shadowY;

            for (let i = 0; i < vertex.length; i += 2) {
                const x = vertex[i];
                const y = vertex[i + 1];

                if (!isNaN(x) && !isNaN(y)) {
                    const transformedX = shadowMatrix[0] * x + shadowMatrix[4] * y + shadowMatrix[12];
                    const transformedY = shadowMatrix[1] * x + shadowMatrix[5] * y + shadowMatrix[13];

                    if (!isNaN(transformedX) && !isNaN(transformedY)) {
                        group.data.positions.push(transformedX, transformedY);
                        group.data.worldPositions.push(transformedX, transformedY);

                        group.data.colors.push(
                            shadowColor[0],
                            shadowColor[1],
                            shadowColor[2],
                            shadowColor[3] * alpha
                        );

                        group.data.uvs.push(0, 0);
                    }
                }
            }

            // 添加索引
            const vertexCount = vertex.length / 2;
            if (indices && indices.length > 0) {
                for (const index of indices) {
                    group.data.indices.push(startVertex + index);
                }
            } else {
                if (vertexCount === 4) {
                    group.data.indices.push(
                        startVertex, startVertex + 1, startVertex + 2,
                        startVertex + 2, startVertex + 3, startVertex
                    );
                } else {
                    for (let i = 1; i < vertexCount - 1; i++) {
                        group.data.indices.push(startVertex, startVertex + i, startVertex + i + 1);
                    }
                }
            }
        }
    }



    // 十六进制颜色转RGBA
    function hexToRgba(hex: string): number[] {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        if (result) {
            return [
                parseInt(result[1], 16) / 255,
                parseInt(result[2], 16) / 255,
                parseInt(result[3], 16) / 255,
                0.5 // 默认阴影透明度
            ];
        }
        return [0, 0, 0, 0.5];
    }


}

test();

function test() {
    // 创建渲染器
    const render = createGlRenderer();

    // 生成测试数据
    const uiTree: Node = {
        children: [
            // 红色矩形
            {
                matrix: [1, 0, 0, 1, 100, 100], // 平移到(100, 100)
                glData: {
                    vertex: [-50, -50, 50, -50, 50, 50, -50, 50], // 矩形顶点
                    indices: [0, 1, 2, 2, 3, 0], // 两个三角形组成矩形
                    rgba: [255, 0, 0, 255] // 红色
                }
            },
            // 绿色矩形
            {
                matrix: [1, 0, 0, 1, 300, 100], // 平移到(300, 100)
                glData: {
                    vertex: [-30, -30, 30, -30, 30, 30, -30, 30],
                    indices: [0, 1, 2, 2, 3, 0],
                    rgba: [0, 255, 0, 255] // 绿色
                }
            },
            // 蓝色三角形
            {
                matrix: [1, 0, 0, 1, 200, 300], // 平移到(200, 300)
                glData: {
                    vertex: [0, -40, -40, 40, 40, 40], // 三角形顶点
                    rgba: [0, 0, 255, 255] // 蓝色
                }
            },
            // 半透明黄色矩形
            {
                matrix: [1, 0, 0, 1, 400, 300],
                alpha: 0.7,
                glData: {
                    vertex: [-25, -25, 25, -25, 25, 25, -25, 25],
                    indices: [0, 1, 2, 2, 3, 0],
                    rgba: [255, 255, 0, 255] // 黄色
                }
            },
            // 带裁剪的容器
            {
                matrix: [1, 0, 0, 1, 600, 300],
                clip: [
                    { x: 550, y: 250 },
                    { x: 650, y: 250 },
                    { x: 650, y: 350 },
                    { x: 550, y: 330 }
                ],
                children: [
                    {
                        matrix: [1, 0, 0, 1, 0, 0],
                        glData: {
                            vertex: [-60, -60, 60, -60, 60, 60, -60, 60],
                            indices: [0, 1, 2, 2, 3, 0],
                            rgba: [0, 255, 255, 255] // 青色，会被裁剪
                        }
                    }
                ]
            },
            // 带阴影的矩形
            {
                matrix: [1, 0, 0, 1, 500, 200],
                shadow: {
                    x: 8,
                    y: 8,
                    blur: 25,
                    color: '#333333'
                },
                glData: {
                    vertex: [-40, -40, 40, -40, 40, 40, -40, 30],
                    indices: [0, 1, 2, 2, 3, 0],
                    rgba: [255, 0, 255, 255] // 紫色
                }
            }
        ]
    };

    const canvas = render(uiTree, { width: 800, height: 600 });
    document.body.appendChild(canvas);

    // 添加标题
    const title = document.createElement('h1');
    title.textContent = 'WebGL2 渲染器测试';
    title.style.textAlign = 'center';
    document.body.insertBefore(title, canvas);

    // 添加说明
    const description = document.createElement('div');
    description.innerHTML = `
        <h3>测试内容：</h3>
        <ul>
            <li>红色矩形 - 基础矩形渲染</li>
            <li>绿色矩形 - 不同位置和大小</li>
            <li>蓝色三角形 - 三角形渲染</li>
            <li>半透明黄色矩形 - Alpha混合测试</li>
            <li>紫色矩形 - 阴影效果测试</li>
            <li>青色矩形 - 裁剪区域测试（部分被裁剪）</li>
        </ul>
        <h3>新增功能：</h3>
        <ul>
            <li><strong>阴影渲染</strong> - 支持阴影偏移、颜色和透明度</li>
            <li><strong>多边形裁剪</strong> - 支持任意多边形裁剪区域</li>
            <li><strong>世界坐标系</strong> - 在片段着色器中进行裁剪计算</li>
        </ul>
    `;
    description.style.margin = '20px';
    document.body.appendChild(description);
}

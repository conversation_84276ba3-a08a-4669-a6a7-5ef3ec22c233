interface CommonStyles {
    alpha?: number;
    clip?: { x: number; y: number }[];
    matrix?: number[];
    shadow?: {
        x: number;
        y: number;
        blur: number;
        color?: string;
    };
}
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[]; //int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number;
    uvs?: number[];
}
type LeafNode = CommonStyles & { glData: GLData }
type ContainerNode = CommonStyles & { children: Node[] }
type Node = LeafNode | ContainerNode;

// 简单的数学库
const mat4 = {
    create(): number[] {
        return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];
    },

    identity(out: number[]): number[] {
        out[0] = 1; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = 1; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    multiply(out: number[], a: number[], b: number[]): number[] {
        const a00 = a[0], a01 = a[1], a02 = a[2], a03 = a[3];
        const a10 = a[4], a11 = a[5], a12 = a[6], a13 = a[7];
        const a20 = a[8], a21 = a[9], a22 = a[10], a23 = a[11];
        const a30 = a[12], a31 = a[13], a32 = a[14], a33 = a[15];

        const b00 = b[0], b01 = b[1], b02 = b[2], b03 = b[3];
        const b10 = b[4], b11 = b[5], b12 = b[6], b13 = b[7];
        const b20 = b[8], b21 = b[9], b22 = b[10], b23 = b[11];
        const b30 = b[12], b31 = b[13], b32 = b[14], b33 = b[15];

        out[0] = a00 * b00 + a01 * b10 + a02 * b20 + a03 * b30;
        out[1] = a00 * b01 + a01 * b11 + a02 * b21 + a03 * b31;
        out[2] = a00 * b02 + a01 * b12 + a02 * b22 + a03 * b32;
        out[3] = a00 * b03 + a01 * b13 + a02 * b23 + a03 * b33;
        out[4] = a10 * b00 + a11 * b10 + a12 * b20 + a13 * b30;
        out[5] = a10 * b01 + a11 * b11 + a12 * b21 + a13 * b31;
        out[6] = a10 * b02 + a11 * b12 + a12 * b22 + a13 * b32;
        out[7] = a10 * b03 + a11 * b13 + a12 * b23 + a13 * b33;
        out[8] = a20 * b00 + a21 * b10 + a22 * b20 + a23 * b30;
        out[9] = a20 * b01 + a21 * b11 + a22 * b21 + a23 * b31;
        out[10] = a20 * b02 + a21 * b12 + a22 * b22 + a23 * b32;
        out[11] = a20 * b03 + a21 * b13 + a22 * b23 + a23 * b33;
        out[12] = a30 * b00 + a31 * b10 + a32 * b20 + a33 * b30;
        out[13] = a30 * b01 + a31 * b11 + a32 * b21 + a33 * b31;
        out[14] = a30 * b02 + a31 * b12 + a32 * b22 + a33 * b32;
        out[15] = a30 * b03 + a31 * b13 + a32 * b23 + a33 * b33;
        return out;
    },

    fromScaling(out: number[], v: number[]): number[] {
        out[0] = v[0]; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = v[1]; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = v[2] || 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    fromTranslation(out: number[], v: number[]): number[] {
        out[0] = 1; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = 1; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = v[0]; out[13] = v[1]; out[14] = v[2] || 0; out[15] = 1;
        return out;
    },

    fromRotation(out: number[], rad: number): number[] {
        const s = Math.sin(rad);
        const c = Math.cos(rad);
        out[0] = c; out[1] = s; out[2] = 0; out[3] = 0;
        out[4] = -s; out[5] = c; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    clone(a: number[]): number[] {
        return [...a];
    },

    translate(out: number[], a: number[], v: number[]): number[] {
        const x = v[0], y = v[1], z = v[2] || 0;
        out[12] = a[0] * x + a[4] * y + a[8] * z + a[12];
        out[13] = a[1] * x + a[5] * y + a[9] * z + a[13];
        out[14] = a[2] * x + a[6] * y + a[10] * z + a[14];
        out[15] = a[3] * x + a[7] * y + a[11] * z + a[15];
        return out;
    },

    invert(out: number[], a: number[]): number[] {
        // 简化版本，只处理2D变换
        const det = a[0] * a[5] - a[1] * a[4];
        if (det === 0) return out;

        const invDet = 1 / det;
        out[0] = a[5] * invDet;
        out[1] = -a[1] * invDet;
        out[4] = -a[4] * invDet;
        out[5] = a[0] * invDet;
        out[12] = (a[4] * a[13] - a[5] * a[12]) * invDet;
        out[13] = (a[1] * a[12] - a[0] * a[13]) * invDet;
        return out;
    },

    ortho(out: number[], left: number, right: number, bottom: number, top: number, near: number, far: number): number[] {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        const nf = 1 / (near - far);
        out[0] = -2 * lr;
        out[1] = 0;
        out[2] = 0;
        out[3] = 0;
        out[4] = 0;
        out[5] = -2 * bt;
        out[6] = 0;
        out[7] = 0;
        out[8] = 0;
        out[9] = 0;
        out[10] = 2 * nf;
        out[11] = 0;
        out[12] = (left + right) * lr;
        out[13] = (top + bottom) * bt;
        out[14] = (far + near) * nf;
        out[15] = 1;
        return out;
    }
};

const vec2 = {
    create(): number[] { return [0, 0]; },
    subtract(out: number[], a: number[], b: number[]): number[] {
        out[0] = a[0] - b[0];
        out[1] = a[1] - b[1];
        return out;
    },
    normalize(out: number[], a: number[]): number[] {
        const len = Math.sqrt(a[0] * a[0] + a[1] * a[1]);
        if (len > 0) {
            out[0] = a[0] / len;
            out[1] = a[1] / len;
        }
        return out;
    },
    dot(a: number[], b: number[]): number {
        return a[0] * b[0] + a[1] * b[1];
    }
};

const vec4 = {
    create(): number[] { return [0, 0, 0, 0]; },
    transformMat4(out: number[], a: number[], m: number[]): number[] {
        const x = a[0], y = a[1], z = a[2], w = a[3];
        out[0] = m[0] * x + m[4] * y + m[8] * z + m[12] * w;
        out[1] = m[1] * x + m[5] * y + m[9] * z + m[13] * w;
        out[2] = m[2] * x + m[6] * y + m[10] * z + m[14] * w;
        out[3] = m[3] * x + m[7] * y + m[11] * z + m[15] * w;
        return out;
    },
    normalize(out: number[], a: number[]): number[] {
        const len = Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2] + a[3] * a[3]);
        if (len > 0) {
            out[0] = a[0] / len;
            out[1] = a[1] / len;
            out[2] = a[2] / len;
            out[3] = a[3] / len;
        }
        return out;
    }
};

export function createGlRenderer() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') as WebGL2RenderingContext;

    if (!gl) {
        throw new Error('WebGL2 is not supported in this browser');
    }

    // 设置默认渲染状态
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    gl.disable(gl.DEPTH_TEST);

    // 初始化WebGL资源
    const program = initShaderProgram(gl);
    const buffers: Record<string, WebGLBuffer> = {};
    const textures: Record<number, WebGLTexture> = {};

    return render;

    function render(node: Node, { width, height }: { width: number; height: number }) {
        canvas.width = width, canvas.height = height;
        // 设置视口
        gl.viewport(0, 0, canvas.width, canvas.height);
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

        // 递归渲染节点树
        renderNode(node, mat4.create());
        return canvas;
    }

    function renderNode(node: Node, parentMatrix: number[]) {
        // 应用当前节点的变换矩阵
        let currentMatrix = parentMatrix.slice();
        if (node.matrix) {
            mat4.multiply(currentMatrix, parentMatrix, node.matrix);
        }

        // 处理裁剪区域
        if (node.clip) {
            setupClipArea(node.clip, currentMatrix);
        }

        // 处理阴影
        if (node.shadow) {
            renderShadow(node, currentMatrix);
        }

        // 渲染当前节点
        if (isLeafNode(node)) {
            renderLeafNode(node, currentMatrix);
        }

        // 递归渲染子节点
        if (isContainerNode(node)) {
            for (const child of node.children) {
                renderNode(child, currentMatrix);
            }
        }
    }

    function isLeafNode(node: Node): node is LeafNode {
        return 'glData' in node;
    }

    function isContainerNode(node: Node): node is ContainerNode {
        return 'children' in node;
    }

    function renderLeafNode(node: LeafNode, matrix: number[]) {
        const { glData } = node;

        // 上传顶点数据
        const nodeKey = JSON.stringify(node);
        if (!buffers[nodeKey]) {
            buffers[nodeKey] = createBuffer(gl!, glData.vertex);
        }

        // 绑定纹理
        if (glData.tex) {
            bindTexture(gl!, glData.tex, textures);
        }

        // 设置uniform
        gl!.uniformMatrix4fv(
            gl!.getUniformLocation(program, 'uMatrix'),
            false,
            matrix
        );

        // 绘制
        if (glData.indices) {
            gl!.drawElements(
                gl!.TRIANGLES,
                glData.indices.length,
                gl!.UNSIGNED_SHORT,
                0
            );
        } else {
            gl!.drawArrays(gl!.TRIANGLES, 0, glData.vertex.length / 2);
        }
    }

    function setupClipArea(clip: { x: number, y: number }[], matrix: number[]) {
        // 将clip多边形转换为裁剪平面方程
        const planes: number[][] = [];

        // 计算每条边的裁剪平面
        for (let i = 0; i < clip.length; i++) {
            const p1 = clip[i];
            const p2 = clip[(i + 1) % clip.length];

            // 计算边向量
            const edge = vec2.subtract(vec2.create(), [p2.x, p2.y], [p1.x, p1.y]);
            // 计算法向量（指向多边形内部）
            const normal = vec2.normalize(vec2.create(), [-edge[1], edge[0]]);

            // 平面方程: normal·(x,y) - normal·p1 = 0
            planes.push([normal[0], normal[1], 0, -vec2.dot(normal, [p1.x, p1.y])]);
        }

        // 应用变换矩阵到裁剪平面
        const invertedMatrix = mat4.invert(mat4.create(), matrix);
        const transformedPlanes = planes.map(plane => {
            const transformed = vec4.transformMat4(vec4.create(), plane, invertedMatrix);
            return vec4.normalize(vec4.create(), transformed);
        });

        // 设置裁剪平面到着色器
        const clipPlanesLocation = gl!.getUniformLocation(program, 'uClipPlanes');
        gl!.uniform4fv(clipPlanesLocation, transformedPlanes.flat());

        // 启用裁剪
        gl!.enable(gl!.SCISSOR_TEST);
    }

    function renderShadow(node: Node, matrix: number[]) {
        if (!node.shadow || !isLeafNode(node)) return;

        // 创建阴影变换矩阵
        const shadowMatrix = mat4.clone(matrix);
        mat4.translate(shadowMatrix, shadowMatrix, [
            node.shadow.x / canvas.width * 2,
            node.shadow.y / canvas.height * 2,
            0
        ]);

        // 设置阴影颜色和透明度
        const color = node.shadow.color ? hexToRgba(node.shadow.color) : [0, 0, 0, 0.5];
        gl!.uniform4f(
            gl!.getUniformLocation(program, 'uColor'),
            color[0], color[1], color[2], color[3] * (node.alpha || 1)
        );

        // 绘制阴影
        renderLeafNode(node as LeafNode, shadowMatrix);

        // 恢复原始颜色
        if ('glData' in node && node.glData.rgba) {
            const rgba = node.glData.rgba.map(v => v / 255);
            gl!.uniform4f(
                gl!.getUniformLocation(program, 'uColor'),
                rgba[0], rgba[1], rgba[2], rgba[3] * (node.alpha || 1)
            );
        }
    }

    function hexToRgba(hex: string): number[] {
        // 简化的hex颜色转换
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;
        const a = hex.length > 7 ? parseInt(hex.slice(7, 9), 16) / 255 : 1;
        return [r, g, b, a];
    }
}

function initShaderProgram(gl: WebGL2RenderingContext): WebGLProgram {
    const vsSource = `#version 300 es
        in vec2 aPosition;
        in vec2 aUV;
        uniform mat4 uMatrix;
        uniform mat4 uProjection;
        out vec2 vUV;

        void main() {
            gl_Position = uProjection * uMatrix * vec4(aPosition, 0.0, 1.0);
            vUV = aUV;
        }
    `;

    const fsSource = `#version 300 es
        precision mediump float;
        in vec2 vUV;
        uniform vec4 uColor;
        uniform sampler2D uTexture;
        uniform bool uUseTexture;
        out vec4 fragColor;

        void main() {
            if (uUseTexture) {
                fragColor = texture(uTexture, vUV) * uColor;
            } else {
                fragColor = uColor;
            }
        }
    `;

    // 创建着色器程序
    const vertexShader = gl.createShader(gl.VERTEX_SHADER)!;
    gl.shaderSource(vertexShader, vsSource);
    gl.compileShader(vertexShader);

    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER)!;
    gl.shaderSource(fragmentShader, fsSource);
    gl.compileShader(fragmentShader);

    // 检查编译错误
    if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
        console.error('Vertex shader error:', gl.getShaderInfoLog(vertexShader));
    }
    if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
        console.error('Fragment shader error:', gl.getShaderInfoLog(fragmentShader));
    }

    // 创建程序并链接
    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Program linking error:', gl.getProgramInfoLog(program));
    }

    gl.useProgram(program);
    return program;
}

function createBuffer(gl: WebGL2RenderingContext, data: number[]): WebGLBuffer {
    const buffer = gl.createBuffer()!;
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(data), gl.STATIC_DRAW);

    // 设置顶点属性指针
    const currentProgram = gl.getParameter(gl.CURRENT_PROGRAM) as WebGLProgram;
    const positionLoc = gl.getAttribLocation(currentProgram, 'aPosition');
    gl.enableVertexAttribArray(positionLoc);
    gl.vertexAttribPointer(positionLoc, 2, gl.FLOAT, false, 0, 0);

    return buffer;
}

function bindTexture(gl: WebGL2RenderingContext, tex: Tex, textures: Record<number, WebGLTexture>): void {
    if (!textures[tex.id]) {
        textures[tex.id] = gl.createTexture()!;
        gl.bindTexture(gl.TEXTURE_2D, textures[tex.id]);

        // 默认纹理参数
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

        // 这里应该加载实际纹理数据，示例中使用1x1白色纹理
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE,
            new Uint8Array([255, 255, 255, 255]));
    }

    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, textures[tex.id]);
    const currentProgram = gl.getParameter(gl.CURRENT_PROGRAM) as WebGLProgram;
    gl.uniform1i(gl.getUniformLocation(currentProgram, 'uTexture'), 0);

    // 设置UV坐标
    if (tex.uvs) {
        const uvBuffer = gl.createBuffer()!;
        gl.bindBuffer(gl.ARRAY_BUFFER, uvBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(tex.uvs), gl.STATIC_DRAW);

        const uvLoc = gl.getAttribLocation(currentProgram, 'aUV');
        gl.enableVertexAttribArray(uvLoc);
        gl.vertexAttribPointer(uvLoc, 2, gl.FLOAT, false, 0, 0);
    }
}


test();

function test() {

    // 创建渲染器
    const render = createGlRenderer();

    // 定义纹理UV坐标
    const fullUVs = [0, 0, 1, 0, 1, 1, 0, 1]; // 完整纹理

    // 创建UI树
    const uiTree = {
        matrix: mat4.fromScaling(mat4.create(), [2, 2]), // 放大2倍
        children: [
            // 红色矩形带阴影
            {
                glData: {
                    vertex: [
                        -0.5, -0.5,  // 左下
                        0.5, -0.5,   // 右下
                        0.5, 0.5,    // 右上
                        -0.5, 0.5    // 左上
                    ],
                    indices: [0, 1, 2, 0, 2, 3], // 两个三角形组成矩形
                    rgba: [255, 0, 0, 255] // 红色
                },
                shadow: {
                    x: 10, y: 10, blur: 5,
                    color: '#00000080' // 半透明黑色阴影
                },
                clip: [ // 裁剪区域 - 右上四分之一
                    { x: 0, y: 0 },
                    { x: 0.5, y: 0 },
                    { x: 0.5, y: 0.5 },
                    { x: 0, y: 0.5 }
                ]
            },

            // 带纹理的图片
            {
                matrix: mat4.fromTranslation(mat4.create(), [0.3, 0.3]), // 向右上偏移
                glData: {
                    vertex: [
                        -0.2, -0.2,
                        0.2, -0.2,
                        0.2, 0.2,
                        -0.2, 0.2
                    ],
                    indices: [0, 1, 2, 0, 2, 3],
                    tex: {
                        id: 1, // 纹理ID
                        uvs: fullUVs
                    }
                },
                alpha: 0.7 // 70%透明度
            },

            // 嵌套的容器
            {
                matrix: mat4.fromRotation(mat4.create(), Math.PI / 4), // 旋转45度
                children: [
                    // 蓝色三角形
                    {
                        glData: {
                            vertex: [
                                0, 0.3,
                                -0.3, -0.3,
                                0.3, -0.3
                            ],
                            rgba: [0, 0, 255, 200] // 半透明蓝色
                        }
                    }
                ]
            }
        ]
    };

    // 加载纹理图片
    function loadTexture(url: string, id: number) {
        const img = new Image();
        img.onload = () => {
            // 实际应用中这里应该更新纹理数据
            console.log(`Texture ${id} loaded: ${url}`);
        };
        img.src = url;
    }

    // 加载示例纹理
    loadTexture('./texture.png', 1);

    const canvas = render(uiTree, { width: 800, height: 600 });
    document.body.appendChild(canvas);
    // 渲染循环
    function animate() {
        render(uiTree, { width: 800, height: 600 });
        requestAnimationFrame(animate);
    }

    // 启动渲染
    // animate();
}
interface CommonStyles {
    alpha?: number;
    clip?: { x: number; y: number }[];
    matrix?: number[];
    shadow?: {
        x: number;
        y: number;
        blur: number;
        color?: string;
    };
}
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[]; //int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number;
    uvs?: number[];
}
type LeafNode = CommonStyles & { glData: GLData }
type ContainerNode = CommonStyles & { children: Node[] }
type Node = LeafNode | ContainerNode;

// 简化的矩阵工具
const mat4 = {
    create(): number[] {
        return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];
    },
    multiply(out: number[], a: number[], b: number[]): number[] {
        // 简化的2D矩阵乘法
        const a00 = a[0], a01 = a[1], a04 = a[4], a05 = a[5], a12 = a[12], a13 = a[13];
        const b00 = b[0], b01 = b[1], b04 = b[4], b05 = b[5], b12 = b[12], b13 = b[13];

        out[0] = a00 * b00 + a01 * b04;
        out[1] = a00 * b01 + a01 * b05;
        out[4] = a04 * b00 + a05 * b04;
        out[5] = a04 * b01 + a05 * b05;
        out[12] = a00 * b12 + a01 * b13 + a12;
        out[13] = a04 * b12 + a05 * b13 + a13;
        return out;
    },
    ortho(out: number[], left: number, right: number, bottom: number, top: number): number[] {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        out[0] = -2 * lr; out[1] = 0; out[4] = 0; out[5] = -2 * bt;
        out[12] = (left + right) * lr; out[13] = (top + bottom) * bt;
        return out;
    }
};

export function createGlRenderer() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') as WebGL2RenderingContext;

    if (!gl) {
        throw new Error('WebGL2 is not supported in this browser');
    }

    // 顶点着色器
    const vertexShaderSource = `#version 300 es
        in vec2 a_position;
        in vec4 a_color;
        in vec2 a_uv;
        uniform mat4 u_matrix;
        uniform mat4 u_projection;
        out vec4 v_color;
        out vec2 v_uv;

        void main() {
            gl_Position = u_projection * u_matrix * vec4(a_position, 0.0, 1.0);
            v_color = a_color;
            v_uv = a_uv;
        }
    `;

    // 片段着色器
    const fragmentShaderSource = `#version 300 es
        precision mediump float;
        in vec4 v_color;
        in vec2 v_uv;
        uniform sampler2D u_texture;
        uniform bool u_use_texture;
        out vec4 fragColor;

        void main() {
            if (u_use_texture) {
                fragColor = texture(u_texture, v_uv) * v_color;
            } else {
                fragColor = v_color;
            }
        }
    `;

    // 创建着色器
    function createShader(type: number, source: string): WebGLShader {
        const shader = gl.createShader(type)!;
        gl.shaderSource(shader, source);
        gl.compileShader(shader);

        if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
            const error = gl.getShaderInfoLog(shader);
            gl.deleteShader(shader);
            throw new Error(`Shader compilation error: ${error}`);
        }

        return shader;
    }

    // 创建程序
    const vertexShader = createShader(gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = createShader(gl.FRAGMENT_SHADER, fragmentShaderSource);
    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        const error = gl.getProgramInfoLog(program);
        throw new Error(`Program linking error: ${error}`);
    }

    // 获取属性和uniform位置
    const positionLocation = gl.getAttribLocation(program, 'a_position');
    const colorLocation = gl.getAttribLocation(program, 'a_color');
    const uvLocation = gl.getAttribLocation(program, 'a_uv');
    const matrixLocation = gl.getUniformLocation(program, 'u_matrix');
    const projectionLocation = gl.getUniformLocation(program, 'u_projection');
    const textureLocation = gl.getUniformLocation(program, 'u_texture');
    const useTextureLocation = gl.getUniformLocation(program, 'u_use_texture');

    // 创建缓冲区
    const positionBuffer = gl.createBuffer();
    const colorBuffer = gl.createBuffer();
    const uvBuffer = gl.createBuffer();
    const indexBuffer = gl.createBuffer();

    // 设置WebGL状态
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);

    // 缓存
    const bufferCache = new Map<string, { positions: number[], colors: number[], uvs: number[], indices: number[] }>();
    const textureCache = new Map<number, WebGLTexture>();

    return render;

    function render(node: Node, { width, height }: { width: number; height: number }) {
        canvas.width = width;
        canvas.height = height;

        // 设置视口
        gl.viewport(0, 0, width, height);
        gl.clearColor(0.9, 0.9, 0.9, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);

        gl.useProgram(program);

        // 设置投影矩阵
        const projectionMatrix = mat4.create();
        mat4.ortho(projectionMatrix, 0, width, height, 0);
        gl.uniformMatrix4fv(projectionLocation, false, projectionMatrix);

        // 收集所有渲染数据
        const renderData = { positions: [], colors: [], uvs: [], indices: [] };
        let vertexOffset = 0;

        collectRenderData(node, mat4.create(), renderData, { vertexOffset: () => vertexOffset, addVertices: (count) => vertexOffset += count });

        // 上传数据到GPU
        if (renderData.positions.length > 0) {
            // 位置数据
            gl.bindBuffer(gl.ARRAY_BUFFER, positionBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.positions), gl.STATIC_DRAW);
            gl.enableVertexAttribArray(positionLocation);
            gl.vertexAttribPointer(positionLocation, 2, gl.FLOAT, false, 0, 0);

            // 颜色数据
            gl.bindBuffer(gl.ARRAY_BUFFER, colorBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.colors), gl.STATIC_DRAW);
            gl.enableVertexAttribArray(colorLocation);
            gl.vertexAttribPointer(colorLocation, 4, gl.FLOAT, false, 0, 0);

            // UV数据
            gl.bindBuffer(gl.ARRAY_BUFFER, uvBuffer);
            gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(renderData.uvs), gl.STATIC_DRAW);
            gl.enableVertexAttribArray(uvLocation);
            gl.vertexAttribPointer(uvLocation, 2, gl.FLOAT, false, 0, 0);

            // 索引数据
            gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, indexBuffer);
            gl.bufferData(gl.ELEMENT_ARRAY_BUFFER, new Uint16Array(renderData.indices), gl.STATIC_DRAW);

            // 设置单位矩阵
            gl.uniformMatrix4fv(matrixLocation, false, mat4.create());
            gl.uniform1i(useTextureLocation, 0);

            // 绘制
            gl.drawElements(gl.TRIANGLES, renderData.indices.length, gl.UNSIGNED_SHORT, 0);
        }

        return canvas;
    }

    // 递归收集渲染数据
    function collectRenderData(node: Node, parentMatrix: number[], renderData: any, context: { vertexOffset: () => number, addVertices: (count: number) => void }) {
        // 计算当前变换矩阵
        let currentMatrix = parentMatrix.slice();
        if (node.matrix) {
            currentMatrix = mat4.multiply(mat4.create(), parentMatrix, node.matrix);
        }

        // 处理叶子节点
        if ('glData' in node) {
            const { vertex, indices, rgba = [255, 255, 255, 255], tex } = node.glData;
            const alpha = node.alpha || 1;
            const startVertex = context.vertexOffset();

            // 变换顶点并添加到渲染数据
            for (let i = 0; i < vertex.length; i += 2) {
                const x = vertex[i];
                const y = vertex[i + 1];

                // 应用变换矩阵
                const transformedX = currentMatrix[0] * x + currentMatrix[4] * y + currentMatrix[12];
                const transformedY = currentMatrix[1] * x + currentMatrix[5] * y + currentMatrix[13];

                renderData.positions.push(transformedX, transformedY);

                // 添加颜色
                renderData.colors.push(
                    rgba[0] / 255,
                    rgba[1] / 255,
                    rgba[2] / 255,
                    (rgba[3] / 255) * alpha
                );

                // 添加UV坐标
                if (tex && tex.uvs) {
                    renderData.uvs.push(tex.uvs[i], tex.uvs[i + 1]);
                } else {
                    // 默认UV坐标
                    renderData.uvs.push(0, 0);
                }
            }

            // 添加索引
            const vertexCount = vertex.length / 2;
            if (indices) {
                for (const index of indices) {
                    renderData.indices.push(startVertex + index);
                }
            } else {
                // 默认三角形索引
                for (let i = 0; i < vertexCount - 2; i++) {
                    renderData.indices.push(startVertex, startVertex + i + 1, startVertex + i + 2);
                }
            }

            context.addVertices(vertexCount);
        }

        // 处理子节点
        if ('children' in node && node.children) {
            for (const child of node.children) {
                collectRenderData(child, currentMatrix, renderData, context);
            }
        }
    }
}

test();

function test() {
    // 创建渲染器
    const render = createGlRenderer();

    // 生成测试数据
    const uiTree: Node = {
        children: [
            // 红色矩形
            {
                matrix: [1, 0, 0, 1, 100, 100], // 平移到(100, 100)
                glData: {
                    vertex: [-50, -50, 50, -50, 50, 50, -50, 50], // 矩形顶点
                    indices: [0, 1, 2, 2, 3, 0], // 两个三角形组成矩形
                    rgba: [255, 0, 0, 255] // 红色
                }
            },
            // 绿色矩形
            {
                matrix: [1, 0, 0, 1, 300, 100], // 平移到(300, 100)
                glData: {
                    vertex: [-30, -30, 30, -30, 30, 30, -30, 30],
                    indices: [0, 1, 2, 2, 3, 0],
                    rgba: [0, 255, 0, 255] // 绿色
                }
            },
            // 蓝色三角形
            {
                matrix: [1, 0, 0, 1, 200, 300], // 平移到(200, 300)
                glData: {
                    vertex: [0, -40, -40, 40, 40, 40], // 三角形顶点
                    rgba: [0, 0, 255, 255] // 蓝色
                }
            },
            // 半透明黄色矩形
            {
                matrix: [1, 0, 0, 1, 400, 300],
                alpha: 0.7,
                glData: {
                    vertex: [-25, -25, 25, -25, 25, 25, -25, 25],
                    indices: [0, 1, 2, 2, 3, 0],
                    rgba: [255, 255, 0, 255] // 黄色
                }
            },
            // 嵌套容器
            {
                matrix: [0.8, 0, 0, 0.8, 500, 200], // 缩放0.8倍并平移
                children: [
                    {
                        matrix: [1, 0, 0, 1, 0, 0],
                        glData: {
                            vertex: [-40, -40, 40, -40, 40, 40, -40, 40],
                            indices: [0, 1, 2, 2, 3, 0],
                            rgba: [255, 0, 255, 255] // 紫色
                        }
                    },
                    {
                        matrix: [1, 0, 0, 1, 60, 60],
                        glData: {
                            vertex: [-20, -20, 20, -20, 20, 20, -20, 20],
                            indices: [0, 1, 2, 2, 3, 0],
                            rgba: [0, 255, 255, 255] // 青色
                        }
                    }
                ]
            }
        ]
    };

    const canvas = render(uiTree, { width: 800, height: 600 });
    document.body.appendChild(canvas);

    // 添加标题
    const title = document.createElement('h1');
    title.textContent = 'WebGL2 渲染器测试';
    title.style.textAlign = 'center';
    document.body.insertBefore(title, canvas);

    // 添加说明
    const description = document.createElement('div');
    description.innerHTML = `
        <h3>测试内容：</h3>
        <ul>
            <li>红色矩形 - 基础矩形渲染</li>
            <li>绿色矩形 - 不同位置和大小</li>
            <li>蓝色三角形 - 三角形渲染</li>
            <li>半透明黄色矩形 - Alpha混合测试</li>
            <li>紫色和青色矩形 - 嵌套变换测试</li>
        </ul>
    `;
    description.style.margin = '20px';
    document.body.appendChild(description);
}

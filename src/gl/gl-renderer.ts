interface CommonStyles {
    alpha?
    clip?: { x, y }[]
    matrix?: number[]
    shadow?: {
        x: number;
        y: number;
        blur: number;
        color?: string;
    }
}
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[]; //int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number;
    uvs?: number[];
}
type LeafNode = CommonStyles & { glData: GLData }
type ContainerNode = CommonStyles & { children: Node[] }
type Node = LeafNode | ContainerNode;

export function createGlRenderer() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (!gl) {
        throw new Error('WebGL2 is not supported in this browser');
    }

    // 设置默认渲染状态
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    gl.disable(gl.DEPTH_TEST);

    // 初始化WebGL资源
    const program = initShaderProgram(gl);
    const buffers: Record<string, WebGLBuffer> = {};
    const textures: Record<number, WebGLTexture> = {};

    return render;

    function render(node: Node, { width, height }) {
        canvas.width = width, canvas.height = height;
        // 设置视口
        gl.viewport(0, 0, canvas.width, canvas.height);
        gl.clearColor(0, 0, 0, 0);
        gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);

        // 递归渲染节点树
        renderNode(node, mat4.create());
        return canvas;
    }

    function renderNode(node: Node, parentMatrix: mat4) {
        // 应用当前节点的变换矩阵
        let currentMatrix = parentMatrix.slice();
        if (node.matrix) {
            mat4.multiply(currentMatrix, parentMatrix, node.matrix);
        }

        // 处理裁剪区域
        if (node.clip) {
            setupClipArea(node.clip, currentMatrix);
        }

        // 处理阴影
        if (node.shadow) {
            renderShadow(node, currentMatrix);
        }

        // 渲染当前节点
        if (isLeafNode(node)) {
            renderLeafNode(node, currentMatrix);
        }

        // 递归渲染子节点
        if (isContainerNode(node)) {
            for (const child of node.children) {
                renderNode(child, currentMatrix);
            }
        }
    }

    function isLeafNode(node: Node): node is LeafNode {
        return 'glData' in node;
    }

    function isContainerNode(node: Node): node is ContainerNode {
        return 'children' in node;
    }

    function renderLeafNode(node: LeafNode, matrix: mat4) {
        const { glData } = node;

        // 上传顶点数据
        if (!buffers[node]) {
            buffers[node] = createBuffer(gl, glData.vertex);
        }

        // 绑定纹理
        if (glData.tex) {
            bindTexture(gl, glData.tex);
        }

        // 设置uniform
        gl.uniformMatrix4fv(
            gl.getUniformLocation(program, 'uMatrix'),
            false,
            matrix
        );

        // 绘制
        if (glData.indices) {
            gl.drawElements(
                gl.TRIANGLES,
                glData.indices.length,
                gl.UNSIGNED_SHORT,
                0
            );
        } else {
            gl.drawArrays(gl.TRIANGLES, 0, glData.vertex.length / 2);
        }
    }

    function setupClipArea(clip: { x: number, y: number }[], matrix: mat4) {
        // 将clip多边形转换为裁剪平面方程
        const planes: vec4[] = [];

        // 计算每条边的裁剪平面
        for (let i = 0; i < clip.length; i++) {
            const p1 = clip[i];
            const p2 = clip[(i + 1) % clip.length];

            // 计算边向量
            const edge = vec2.subtract(vec2.create(), p2, p1);
            // 计算法向量（指向多边形内部）
            const normal = vec2.normalize(vec2.create(), [-edge[1], edge[0]]);

            // 平面方程: normal·(x,y) - normal·p1 = 0
            planes.push([normal[0], normal[1], 0, -vec2.dot(normal, p1)]);
        }

        // 应用变换矩阵到裁剪平面
        const invertedMatrix = mat4.invert(mat4.create(), matrix);
        const transformedPlanes = planes.map(plane => {
            const transformed = vec4.transformMat4(vec4.create(), plane, invertedMatrix);
            return vec4.normalize(vec4.create(), transformed);
        });

        // 设置裁剪平面到着色器
        const clipPlanesLocation = gl.getUniformLocation(program, 'uClipPlanes');
        gl.uniform4fv(clipPlanesLocation, transformedPlanes.flat());

        // 启用裁剪
        gl.enable(gl.SCISSOR_TEST);
    }

    function renderShadow(node: Node, matrix: mat4) {
        if (!node.shadow || !isLeafNode(node)) return;

        // 创建阴影变换矩阵
        const shadowMatrix = mat4.clone(matrix);
        mat4.translate(shadowMatrix, shadowMatrix, [
            node.shadow.x / canvas.width * 2,
            node.shadow.y / canvas.height * 2,
            0
        ]);

        // 设置阴影颜色和透明度
        const color = node.shadow.color ? hexToRgba(node.shadow.color) : [0, 0, 0, 0.5];
        gl.uniform4f(
            gl.getUniformLocation(program, 'uColor'),
            color[0], color[1], color[2], color[3] * (node.alpha || 1)
        );

        // 绘制阴影
        renderLeafNode(node as LeafNode, shadowMatrix);

        // 恢复原始颜色
        if (node.glData.rgba) {
            const rgba = node.glData.rgba.map(v => v / 255);
            gl.uniform4f(
                gl.getUniformLocation(program, 'uColor'),
                rgba[0], rgba[1], rgba[2], rgba[3] * (node.alpha || 1)
            );
        }
    }

    function hexToRgba(hex: string): number[] {
        // 简化的hex颜色转换
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;
        const a = hex.length > 7 ? parseInt(hex.slice(7, 9), 16) / 255 : 1;
        return [r, g, b, a];
    }
}

function initShaderProgram(gl: WebGL2RenderingContext): WebGLProgram {
    const vsSource = `...`; // 保持之前的顶点着色器代码
    const fsSource = `...`; // 保持之前的片段着色器代码

    // 创建着色器程序
    const vertexShader = gl.createShader(gl.VERTEX_SHADER)!;
    gl.shaderSource(vertexShader, vsSource);
    gl.compileShader(vertexShader);

    const fragmentShader = gl.createShader(gl.FRAGMENT_SHADER)!;
    gl.shaderSource(fragmentShader, fsSource);
    gl.compileShader(fragmentShader);

    // 检查编译错误
    if (!gl.getShaderParameter(vertexShader, gl.COMPILE_STATUS)) {
        console.error('Vertex shader error:', gl.getShaderInfoLog(vertexShader));
    }
    if (!gl.getShaderParameter(fragmentShader, gl.COMPILE_STATUS)) {
        console.error('Fragment shader error:', gl.getShaderInfoLog(fragmentShader));
    }

    // 创建程序并链接
    const program = gl.createProgram()!;
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
        console.error('Program linking error:', gl.getProgramInfoLog(program));
    }

    gl.useProgram(program);
    return program;
}

function createBuffer(gl: WebGL2RenderingContext, data: number[]): WebGLBuffer {
    const buffer = gl.createBuffer()!;
    gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
    gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(data), gl.STATIC_DRAW);

    // 设置顶点属性指针
    const positionLoc = gl.getAttribLocation(gl.getParameter(gl.CURRENT_PROGRAM), 'aPosition');
    gl.enableVertexAttribArray(positionLoc);
    gl.vertexAttribPointer(positionLoc, 2, gl.FLOAT, false, 0, 0);

    return buffer;
}

function bindTexture(gl: WebGL2RenderingContext, tex: Tex): void {
    if (!textures[tex.id]) {
        textures[tex.id] = gl.createTexture()!;
        gl.bindTexture(gl.TEXTURE_2D, textures[tex.id]);

        // 默认纹理参数
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);

        // 这里应该加载实际纹理数据，示例中使用1x1白色纹理
        gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, 1, 1, 0, gl.RGBA, gl.UNSIGNED_BYTE,
            new Uint8Array([255, 255, 255, 255]));
    }

    gl.activeTexture(gl.TEXTURE0);
    gl.bindTexture(gl.TEXTURE_2D, textures[tex.id]);
    gl.uniform1i(gl.getUniformLocation(gl.getParameter(gl.CURRENT_PROGRAM), 'uTexture'), 0);

    // 设置UV坐标
    if (tex.uvs) {
        const uvBuffer = gl.createBuffer()!;
        gl.bindBuffer(gl.ARRAY_BUFFER, uvBuffer);
        gl.bufferData(gl.ARRAY_BUFFER, new Float32Array(tex.uvs), gl.STATIC_DRAW);

        const uvLoc = gl.getAttribLocation(gl.getParameter(gl.CURRENT_PROGRAM), 'aUV');
        gl.enableVertexAttribArray(uvLoc);
        gl.vertexAttribPointer(uvLoc, 2, gl.FLOAT, false, 0, 0);
    }
}


test();

function test() {

    // 创建渲染器
    const render = createGlRenderer();

    // 定义纹理UV坐标
    const fullUVs = [0, 0, 1, 0, 1, 1, 0, 1]; // 完整纹理

    // 创建UI树
    const uiTree = {
        matrix: mat4.fromScaling(mat4.create(), [2, 2]), // 放大2倍
        children: [
            // 红色矩形带阴影
            {
                glData: {
                    vertex: [
                        -0.5, -0.5,  // 左下
                        0.5, -0.5,   // 右下
                        0.5, 0.5,    // 右上
                        -0.5, 0.5    // 左上
                    ],
                    indices: [0, 1, 2, 0, 2, 3], // 两个三角形组成矩形
                    rgba: [255, 0, 0, 255] // 红色
                },
                shadow: {
                    x: 10, y: 10, blur: 5,
                    color: '#00000080' // 半透明黑色阴影
                },
                clip: [ // 裁剪区域 - 右上四分之一
                    { x: 0, y: 0 },
                    { x: 0.5, y: 0 },
                    { x: 0.5, y: 0.5 },
                    { x: 0, y: 0.5 }
                ]
            },

            // 带纹理的图片
            {
                matrix: mat4.fromTranslation(mat4.create(), [0.3, 0.3]), // 向右上偏移
                glData: {
                    vertex: [
                        -0.2, -0.2,
                        0.2, -0.2,
                        0.2, 0.2,
                        -0.2, 0.2
                    ],
                    indices: [0, 1, 2, 0, 2, 3],
                    tex: {
                        id: 1, // 纹理ID
                        uvs: fullUVs
                    }
                },
                alpha: 0.7 // 70%透明度
            },

            // 嵌套的容器
            {
                matrix: mat4.fromRotation(mat4.create(), Math.PI / 4), // 旋转45度
                children: [
                    // 蓝色三角形
                    {
                        glData: {
                            vertex: [
                                0, 0.3,
                                -0.3, -0.3,
                                0.3, -0.3
                            ],
                            rgba: [0, 0, 255, 200] // 半透明蓝色
                        }
                    }
                ]
            }
        ]
    };

    // 加载纹理图片
    function loadTexture(url: string, id: number) {
        const img = new Image();
        img.onload = () => {
            // 实际应用中这里应该更新纹理数据
            console.log(`Texture ${id} loaded: ${url}`);
        };
        img.src = url;
    }

    // 加载示例纹理
    loadTexture('texture.png', 1);

    const canvas = render(uiTree, { width: 800, height: 600 });
    document.body.appendChild(canvas);
    // 渲染循环
    function animate() {
        render(uiTree);
        requestAnimationFrame(animate);
    }

    // 启动渲染
    // animate();
}
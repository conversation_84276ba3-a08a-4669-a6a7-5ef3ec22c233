interface CommonStyles {
    alpha?: number;
    clip?: { x: number; y: number }[];
    matrix?: number[];
    shadow?: {
        x: number;
        y: number;
        blur: number;
        color?: string;
    };
}
interface GLData {
    vertex: number[];
    indices?: number[];
    rgba?: number[]; //int[], 0-255
    tex?: Tex;
}
interface Tex {
    id: number;
    uvs?: number[];
}
type LeafNode = CommonStyles & { glData: GLData }
type ContainerNode = CommonStyles & { children: Node[] }
type Node = LeafNode | ContainerNode;


export function createGlRenderer() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') as WebGL2RenderingContext;

    if (!gl) {
        throw new Error('WebGL2 is not supported in this browser');
    }


    return render;

    function render(node: Node, { width, height }: { width: number; height: number }) {
        canvas.width = width, canvas.height = height;
        ///渲染代码
        return canvas;
    }

}


test();

function test() {

    // 创建渲染器
    const render = createGlRenderer();

//生成测试数据...
    const uiTree = {};
    const canvas = render(uiTree, { width: 800, height: 600 });
    document.body.appendChild(canvas);

}

// 简单的数学库
const mat4 = {
    create(): number[] {
        return [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1];
    },

    identity(out: number[]): number[] {
        out[0] = 1; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = 1; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    multiply(out: number[], a: number[], b: number[]): number[] {
        const a00 = a[0], a01 = a[1], a02 = a[2], a03 = a[3];
        const a10 = a[4], a11 = a[5], a12 = a[6], a13 = a[7];
        const a20 = a[8], a21 = a[9], a22 = a[10], a23 = a[11];
        const a30 = a[12], a31 = a[13], a32 = a[14], a33 = a[15];

        const b00 = b[0], b01 = b[1], b02 = b[2], b03 = b[3];
        const b10 = b[4], b11 = b[5], b12 = b[6], b13 = b[7];
        const b20 = b[8], b21 = b[9], b22 = b[10], b23 = b[11];
        const b30 = b[12], b31 = b[13], b32 = b[14], b33 = b[15];

        out[0] = a00 * b00 + a01 * b10 + a02 * b20 + a03 * b30;
        out[1] = a00 * b01 + a01 * b11 + a02 * b21 + a03 * b31;
        out[2] = a00 * b02 + a01 * b12 + a02 * b22 + a03 * b32;
        out[3] = a00 * b03 + a01 * b13 + a02 * b23 + a03 * b33;
        out[4] = a10 * b00 + a11 * b10 + a12 * b20 + a13 * b30;
        out[5] = a10 * b01 + a11 * b11 + a12 * b21 + a13 * b31;
        out[6] = a10 * b02 + a11 * b12 + a12 * b22 + a13 * b32;
        out[7] = a10 * b03 + a11 * b13 + a12 * b23 + a13 * b33;
        out[8] = a20 * b00 + a21 * b10 + a22 * b20 + a23 * b30;
        out[9] = a20 * b01 + a21 * b11 + a22 * b21 + a23 * b31;
        out[10] = a20 * b02 + a21 * b12 + a22 * b22 + a23 * b32;
        out[11] = a20 * b03 + a21 * b13 + a22 * b23 + a23 * b33;
        out[12] = a30 * b00 + a31 * b10 + a32 * b20 + a33 * b30;
        out[13] = a30 * b01 + a31 * b11 + a32 * b21 + a33 * b31;
        out[14] = a30 * b02 + a31 * b12 + a32 * b22 + a33 * b32;
        out[15] = a30 * b03 + a31 * b13 + a32 * b23 + a33 * b33;
        return out;
    },

    fromScaling(out: number[], v: number[]): number[] {
        out[0] = v[0]; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = v[1]; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = v[2] || 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    fromTranslation(out: number[], v: number[]): number[] {
        out[0] = 1; out[1] = 0; out[2] = 0; out[3] = 0;
        out[4] = 0; out[5] = 1; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = v[0]; out[13] = v[1]; out[14] = v[2] || 0; out[15] = 1;
        return out;
    },

    fromRotation(out: number[], rad: number): number[] {
        const s = Math.sin(rad);
        const c = Math.cos(rad);
        out[0] = c; out[1] = s; out[2] = 0; out[3] = 0;
        out[4] = -s; out[5] = c; out[6] = 0; out[7] = 0;
        out[8] = 0; out[9] = 0; out[10] = 1; out[11] = 0;
        out[12] = 0; out[13] = 0; out[14] = 0; out[15] = 1;
        return out;
    },

    clone(a: number[]): number[] {
        return [...a];
    },

    translate(out: number[], a: number[], v: number[]): number[] {
        const x = v[0], y = v[1], z = v[2] || 0;
        out[12] = a[0] * x + a[4] * y + a[8] * z + a[12];
        out[13] = a[1] * x + a[5] * y + a[9] * z + a[13];
        out[14] = a[2] * x + a[6] * y + a[10] * z + a[14];
        out[15] = a[3] * x + a[7] * y + a[11] * z + a[15];
        return out;
    },

    invert(out: number[], a: number[]): number[] {
        // 简化版本，只处理2D变换
        const det = a[0] * a[5] - a[1] * a[4];
        if (det === 0) return out;

        const invDet = 1 / det;
        out[0] = a[5] * invDet;
        out[1] = -a[1] * invDet;
        out[4] = -a[4] * invDet;
        out[5] = a[0] * invDet;
        out[12] = (a[4] * a[13] - a[5] * a[12]) * invDet;
        out[13] = (a[1] * a[12] - a[0] * a[13]) * invDet;
        return out;
    },

    ortho(out: number[], left: number, right: number, bottom: number, top: number, near: number, far: number): number[] {
        const lr = 1 / (left - right);
        const bt = 1 / (bottom - top);
        const nf = 1 / (near - far);
        out[0] = -2 * lr;
        out[1] = 0;
        out[2] = 0;
        out[3] = 0;
        out[4] = 0;
        out[5] = -2 * bt;
        out[6] = 0;
        out[7] = 0;
        out[8] = 0;
        out[9] = 0;
        out[10] = 2 * nf;
        out[11] = 0;
        out[12] = (left + right) * lr;
        out[13] = (top + bottom) * bt;
        out[14] = (far + near) * nf;
        out[15] = 1;
        return out;
    }
};

const vec2 = {
    create(): number[] { return [0, 0]; },
    subtract(out: number[], a: number[], b: number[]): number[] {
        out[0] = a[0] - b[0];
        out[1] = a[1] - b[1];
        return out;
    },
    normalize(out: number[], a: number[]): number[] {
        const len = Math.sqrt(a[0] * a[0] + a[1] * a[1]);
        if (len > 0) {
            out[0] = a[0] / len;
            out[1] = a[1] / len;
        }
        return out;
    },
    dot(a: number[], b: number[]): number {
        return a[0] * b[0] + a[1] * b[1];
    }
};

const vec4 = {
    create(): number[] { return [0, 0, 0, 0]; },
    transformMat4(out: number[], a: number[], m: number[]): number[] {
        const x = a[0], y = a[1], z = a[2], w = a[3];
        out[0] = m[0] * x + m[4] * y + m[8] * z + m[12] * w;
        out[1] = m[1] * x + m[5] * y + m[9] * z + m[13] * w;
        out[2] = m[2] * x + m[6] * y + m[10] * z + m[14] * w;
        out[3] = m[3] * x + m[7] * y + m[11] * z + m[15] * w;
        return out;
    },
    normalize(out: number[], a: number[]): number[] {
        const len = Math.sqrt(a[0] * a[0] + a[1] * a[1] + a[2] * a[2] + a[3] * a[3]);
        if (len > 0) {
            out[0] = a[0] / len;
            out[1] = a[1] / len;
            out[2] = a[2] / len;
            out[3] = a[3] / len;
        }
        return out;
    }
};
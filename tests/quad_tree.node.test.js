// Node.js版本的四叉树测试
// 这样我们就能真正看到测试结果了

// 四叉树实现 - 与HTML版本完全一致
class QuadTree {
    constructor(bounds, maxNodes = 10, maxDepth = 5, depth = 0) {
        this.bounds = bounds;
        this.nodes = [];
        this.children = [];
        this.maxNodes = maxNodes;
        this.maxDepth = maxDepth;
        this.depth = depth;
        this.divided = false;
    }

    containsBounds(bounds) {
        return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
               bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
    }

    intersects(range) {
        const c1 = range.maxX < this.bounds.minX;
        const c2 = range.minX > this.bounds.maxX;
        const c3 = range.maxY < this.bounds.minY;
        const c4 = range.minY > this.bounds.maxY;

        const orResult = c1 || c2 || c3 || c4;
        const result = !orResult;

        // 调试特定查询
        if (this.depth === 0 && range.minX === -50) {
            console.log(`    [intersects] c1=${c1}, c2=${c2}, c3=${c3}, c4=${c4}`);
            console.log(`    [intersects] orResult=${orResult}, result=${result}`);
        }

        return result;
    }

    boundsIntersect(bounds1, bounds2) {
        return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                 bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
    }

    subdivide() {
        if (this.divided || this.depth >= this.maxDepth) return;

        const { minX, minY, maxX, maxY } = this.bounds;
        const midX = (minX + maxX) / 2;
        const midY = (minY + maxY) / 2;

        this.children = [
            new QuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
        ];

        this.divided = true;

        const nodesToRedistribute = [...this.nodes];
        this.nodes = [];

        for (const node of nodesToRedistribute) {
            this.insert(node);
        }
    }

    insert(node) {
        if (!this.containsBounds(node.bounds)) {
            return false;
        }

        if (this.nodes.length < this.maxNodes && !this.divided) {
            this.nodes.push(node);
            return true;
        }

        if (!this.divided) {
            this.subdivide();
        }

        for (const child of this.children) {
            if (child.insert(node)) {
                return true;
            }
        }

        this.nodes.push(node);
        return true;
    }

    query(range, found = [], debug = false) {
        if (debug) {
            console.log(`    查询深度 ${this.depth}, 边界:`, this.bounds);
            console.log(`    查询范围:`, range);
        }

        const intersects = this.intersects(range);
        if (debug) {
            console.log(`    深度 ${this.depth} 是否相交: ${intersects}`);
        }

        if (!intersects) {
            return found;
        }

        if (debug) {
            console.log(`    深度 ${this.depth} 检查 ${this.nodes.length} 个节点`);
        }

        for (const node of this.nodes) {
            const nodeIntersects = this.boundsIntersect(range, node.bounds);
            if (debug) {
                console.log(`      节点 ${node.id}: ${JSON.stringify(node.bounds)} -> 相交: ${nodeIntersects}`);
            }
            if (nodeIntersects) {
                found.push(node);
                if (debug) {
                    console.log(`      找到节点 ${node.id}`);
                }
            }
        }

        if (this.divided) {
            if (debug) {
                console.log(`    深度 ${this.depth} 已分割，递归查询 ${this.children.length} 个子象限`);
            }
            for (let i = 0; i < this.children.length; i++) {
                if (debug) {
                    console.log(`    查询子象限 ${i}:`);
                }
                this.children[i].query(range, found, debug);
            }
        }

        return found;
    }

    remove(nodeId) {
        const index = this.nodes.findIndex(node => node.id === nodeId);
        if (index !== -1) {
            this.nodes.splice(index, 1);
            return true;
        }

        if (this.divided) {
            for (const child of this.children) {
                if (child.remove(nodeId)) {
                    return true;
                }
            }
        }

        return false;
    }

    update(nodeId, newBounds) {
        if (this.remove(nodeId)) {
            return this.insert({ id: nodeId, bounds: newBounds });
        }
        return false;
    }

    getStats() {
        let totalNodes = this.nodes.length;
        let maxDepth = this.depth;
        let leafNodes = this.divided ? 0 : 1;

        if (this.divided) {
            for (const child of this.children) {
                const childStats = child.getStats();
                totalNodes += childStats.totalNodes;
                maxDepth = Math.max(maxDepth, childStats.depth);
                leafNodes += childStats.leafNodes;
            }
        }

        return { totalNodes, depth: maxDepth, leafNodes };
    }
}

function createQuadTree(bounds, maxNodes = 10, maxDepth = 5) {
    return new QuadTree(bounds, maxNodes, maxDepth);
}

// 测试框架
class TestRunner {
    constructor() {
        this.tests = [];
        this.results = [];
    }

    test(name, fn) {
        this.tests.push({ name, fn });
    }

    async run() {
        console.log('🧪 开始运行四叉树测试\n');
        
        for (const test of this.tests) {
            try {
                const result = await test.fn();
                this.results.push({ name: test.name, success: true, result });
                console.log(`✅ ${test.name}: ${result}`);
            } catch (error) {
                this.results.push({ name: test.name, success: false, error: error.message });
                console.log(`❌ ${test.name}: ${error.message}`);
            }
        }

        this.printSummary();
    }

    printSummary() {
        const passed = this.results.filter(r => r.success).length;
        const total = this.results.length;
        
        console.log('\n📊 测试总结:');
        console.log(`通过: ${passed}/${total}`);
        console.log(`成功率: ${(passed/total*100).toFixed(1)}%`);
        
        if (passed === total) {
            console.log('🎉 所有测试都通过了！');
        } else {
            console.log('❌ 仍有测试失败');
        }
    }
}

// 创建测试实例
const runner = new TestRunner();

// 测试1: 重现原始查询问题
runner.test('测试3: 查询节点', () => {
    const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
    const quadTree = createQuadTree(bounds, 5, 4);

    console.log('  🔍 调试信息:');
    console.log('  四叉树边界:', bounds);

    const testNodes = [
        { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
        { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
        { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
    ];

    console.log('  要插入的节点:', testNodes);

    // 插入节点
    testNodes.forEach(node => {
        const inserted = quadTree.insert(node);
        console.log(`  插入节点 ${node.id}: ${inserted}`);
        if (!inserted) {
            throw new Error(`插入节点 ${node.id} 失败`);
        }
    });

    // 验证插入后的状态
    const allNodes = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
    console.log('  插入后查询所有节点:', allNodes.map(n => n.id));

    // 查询节点
    const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
    console.log('  查询范围:', queryRange);

    // 直接测试intersects方法
    console.log('  直接测试intersects方法:');
    const directIntersects = quadTree.intersects(queryRange);
    console.log(`  quadTree.intersects(queryRange) = ${directIntersects}`);

    // 手动计算intersects
    console.log('  详细计算过程:');
    console.log(`    queryRange.maxX (${queryRange.maxX}) < quadTree.bounds.minX (${quadTree.bounds.minX}) = ${queryRange.maxX < quadTree.bounds.minX}`);
    console.log(`    queryRange.minX (${queryRange.minX}) > quadTree.bounds.maxX (${quadTree.bounds.maxX}) = ${queryRange.minX > quadTree.bounds.maxX}`);
    console.log(`    queryRange.maxY (${queryRange.maxY}) < quadTree.bounds.minY (${quadTree.bounds.minY}) = ${queryRange.maxY < quadTree.bounds.minY}`);
    console.log(`    queryRange.minY (${queryRange.minY}) > quadTree.bounds.maxY (${quadTree.bounds.maxY}) = ${queryRange.minY > quadTree.bounds.maxY}`);

    const condition1 = queryRange.maxX < quadTree.bounds.minX;
    const condition2 = queryRange.minX > quadTree.bounds.maxX;
    const condition3 = queryRange.maxY < quadTree.bounds.minY;
    const condition4 = queryRange.minY > quadTree.bounds.maxY;
    const orResult = condition1 || condition2 || condition3 || condition4;
    const manualIntersects = !orResult;

    console.log(`  condition1: ${condition1}, condition2: ${condition2}, condition3: ${condition3}, condition4: ${condition4}`);
    console.log(`  orResult: ${orResult}, !orResult: ${manualIntersects}`);
    console.log(`  手动计算intersects = ${manualIntersects}`);

    console.log('  详细查询过程:');

    const found = quadTree.query(queryRange, [], true);
    console.log('  查询结果:', found.map(n => n.id));

    // 手动验证期望结果
    let expectedCount = 0;
    console.log('  手动验证相交逻辑:');
    testNodes.forEach(node => {
        const c1 = queryRange.maxX < node.bounds.minX;
        const c2 = queryRange.minX > node.bounds.maxX;
        const c3 = queryRange.maxY < node.bounds.minY;
        const c4 = queryRange.minY > node.bounds.maxY;
        const intersects = !(c1 || c2 || c3 || c4);

        console.log(`    节点 ${node.id}: ${JSON.stringify(node.bounds)}`);
        console.log(`      ${queryRange.maxX} < ${node.bounds.minX} = ${c1}`);
        console.log(`      ${queryRange.minX} > ${node.bounds.maxX} = ${c2}`);
        console.log(`      ${queryRange.maxY} < ${node.bounds.minY} = ${c3}`);
        console.log(`      ${queryRange.minY} > ${node.bounds.maxY} = ${c4}`);
        console.log(`      相交: ${intersects}`);

        if (intersects) expectedCount++;
    });

    console.log(`  期望找到 ${expectedCount} 个节点，实际找到 ${found.length} 个`);

    if (found.length !== expectedCount) {
        throw new Error(`查询到 ${found.length} 个节点，期望 ${expectedCount} 个`);
    }

    return `查询到 ${found.length} 个节点，期望 ${expectedCount} 个 ✓`;
});

// 测试2: 重现原始更新问题
runner.test('动态测试7: 验证更新结果', () => {
    const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
    const quadTree = createQuadTree(bounds, 5, 4);

    console.log('  🔍 调试更新操作:');

    const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
    console.log('  插入初始节点:', initialNode);
    const insertResult = quadTree.insert(initialNode);
    console.log('  插入结果:', insertResult);

    // 验证初始插入
    const initialQuery = quadTree.query({ minX: -50, minY: -50, maxX: 150, maxY: 150 });
    console.log('  初始位置查询结果:', initialQuery.map(n => n.id));

    // 更新节点位置
    const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
    console.log('  更新到新边界:', newBounds);
    const updateResult = quadTree.update('dynamic1', newBounds);
    console.log('  更新结果:', updateResult);

    if (!updateResult) {
        throw new Error('更新节点失败');
    }

    // 查询整个树看看节点在哪里
    const allNodesAfterUpdate = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
    console.log('  更新后所有节点:', allNodesAfterUpdate.map(n => `${n.id}: ${JSON.stringify(n.bounds)}`));

    // 验证更新后位置
    const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
    console.log('  新位置查询范围:', newQueryRange);
    const foundAfterUpdate = quadTree.query(newQueryRange);
    console.log('  新位置查询结果:', foundAfterUpdate.map(n => n.id));
    const foundUpdated = foundAfterUpdate.find(n => n.id === 'dynamic1');

    if (!foundUpdated) {
        throw new Error('在新位置未找到更新的节点');
    }

    // 验证旧位置没有节点
    const oldQueryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
    console.log('  旧位置查询范围:', oldQueryRange);
    const foundInOldPos = quadTree.query(oldQueryRange);
    console.log('  旧位置查询结果:', foundInOldPos.map(n => n.id));
    const stillInOldPos = foundInOldPos.find(n => n.id === 'dynamic1');

    if (stillInOldPos) {
        throw new Error('在旧位置仍然找到节点');
    }

    return '在新位置找到更新的节点，旧位置已清理 ✓';
});

// 测试3: 批量操作测试
runner.test('批量操作测试', () => {
    const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
    const quadTree = createQuadTree(bounds, 3, 3);
    
    // 插入多个节点
    const manyNodes = [];
    for (let i = 0; i < 10; i++) {
        const x = (i % 3) * 200;
        const y = Math.floor(i / 3) * 200;
        manyNodes.push({
            id: `node${i}`,
            bounds: { minX: x, minY: y, maxX: x + 50, maxY: y + 50 }
        });
    }

    manyNodes.forEach(node => {
        if (!quadTree.insert(node)) {
            throw new Error(`插入节点 ${node.id} 失败`);
        }
    });

    // 查询所有节点
    const allFound = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
    if (allFound.length !== manyNodes.length) {
        throw new Error(`查询到 ${allFound.length} 个节点，期望 ${manyNodes.length} 个`);
    }

    // 删除一些节点
    const toDelete = ['node1', 'node3', 'node5'];
    toDelete.forEach(id => {
        if (!quadTree.remove(id)) {
            throw new Error(`删除节点 ${id} 失败`);
        }
    });

    // 验证删除结果
    const afterDelete = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
    const expectedAfterDelete = manyNodes.length - toDelete.length;
    if (afterDelete.length !== expectedAfterDelete) {
        throw new Error(`删除后剩余 ${afterDelete.length} 个节点，期望 ${expectedAfterDelete} 个`);
    }

    return `批量操作成功：插入${manyNodes.length}个，删除${toDelete.length}个，剩余${afterDelete.length}个 ✓`;
});

// 运行测试
runner.run().catch(console.error);

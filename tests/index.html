<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四叉树测试套件</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        .test-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }
        .test-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .test-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        .test-features {
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }
        .test-features li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        .test-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .test-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-align: center;
            width: 100%;
            box-sizing: border-box;
        }
        .test-button:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .info-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .info-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        .info-content {
            color: #666;
            line-height: 1.6;
        }
        .tech-stack {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        .tech-tag {
            background: #f8f9fa;
            color: #495057;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.9em;
            border: 1px solid #dee2e6;
        }
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌳 四叉树测试套件</h1>
        
        <div class="info-section">
            <div class="info-title">测试概述</div>
            <div class="info-content">
                这个测试套件全面验证四叉树的功能和性能，包括基础操作、视口查询、动态更新和大规模性能测试。
                所有测试都在浏览器环境中运行，支持实时可视化和交互操作。
            </div>
            <div class="tech-stack">
                <span class="tech-tag">TypeScript</span>
                <span class="tech-tag">WebGL</span>
                <span class="tech-tag">Canvas 2D</span>
                <span class="tech-tag">ES6 Modules</span>
                <span class="tech-tag">Performance API</span>
            </div>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">🧪 功能测试</div>
                <div class="test-description">
                    全面测试四叉树的基础功能，包括创建、插入、查询、删除和更新操作。
                    验证各种边界条件和错误处理。
                </div>
                <ul class="test-features">
                    <li>基础CRUD操作测试</li>
                    <li>边界条件验证</li>
                    <li>错误处理测试</li>
                    <li>数据完整性检查</li>
                    <li>统计信息验证</li>
                </ul>
                <a href="quad_tree.test.html" class="test-button">运行功能测试</a>
            </div>

            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">👁️ 可视化测试</div>
                <div class="test-description">
                    交互式可视化界面，实时展示四叉树的结构和查询过程。
                    支持手动添加节点和拖拽查询区域。
                </div>
                <ul class="test-features">
                    <li>实时四叉树可视化</li>
                    <li>交互式节点添加</li>
                    <li>拖拽查询区域</li>
                    <li>动态参数调整</li>
                    <li>查询结果高亮</li>
                </ul>
                <a href="quad_tree_visual.html" class="test-button">打开可视化测试</a>
            </div>

            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">⚡ 性能基准</div>
                <div class="test-description">
                    大规模性能测试，评估四叉树在不同数据量和配置下的表现。
                    包括插入、查询速度和可扩展性分析。
                </div>
                <ul class="test-features">
                    <li>大规模插入性能</li>
                    <li>查询速度基准</li>
                    <li>可扩展性测试</li>
                    <li>配置参数对比</li>
                    <li>内存使用分析</li>
                </ul>
                <a href="quad_tree_benchmark.html" class="test-button">运行性能测试</a>
            </div>

            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">🎯 视口查询</div>
                <div class="test-description">
                    专门测试视口查询功能，验证矩阵变换后的空间索引和动态视口计算。
                    模拟真实的渲染场景。
                </div>
                <ul class="test-features">
                    <li>矩阵变换支持</li>
                    <li>视口边界计算</li>
                    <li>空间索引构建</li>
                    <li>动态视口更新</li>
                    <li>渲染优化验证</li>
                </ul>
                <a href="javascript:runViewportTest()" class="test-button">测试视口查询</a>
            </div>

            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">🔄 动态操作</div>
                <div class="test-description">
                    测试四叉树的动态增删改操作，验证树结构的自动调整和数据一致性。
                    模拟实时数据更新场景。
                </div>
                <ul class="test-features">
                    <li>动态节点添加</li>
                    <li>节点删除操作</li>
                    <li>位置更新测试</li>
                    <li>树结构重平衡</li>
                    <li>数据一致性验证</li>
                </ul>
                <a href="javascript:runDynamicTest()" class="test-button">测试动态操作</a>
            </div>

            <div class="test-card">
                <div class="status-indicator"></div>
                <div class="test-title">📊 集成测试</div>
                <div class="test-description">
                    与WebGL渲染系统的集成测试，验证四叉树在实际渲染管线中的表现。
                    测试缓冲区管理和视口裁剪。
                </div>
                <ul class="test-features">
                    <li>WebGL集成测试</li>
                    <li>缓冲区管理验证</li>
                    <li>视口裁剪优化</li>
                    <li>渲染性能分析</li>
                    <li>内存使用监控</li>
                </ul>
                <a href="../src/gl_demo.html" class="test-button">运行集成测试</a>
            </div>
        </div>

        <div class="info-section">
            <div class="info-title">测试说明</div>
            <div class="info-content">
                <p><strong>运行环境：</strong>所有测试都在现代浏览器中运行，推荐使用 Chrome 或 Firefox 的最新版本。</p>
                <p><strong>测试数据：</strong>测试使用随机生成的节点数据，可以通过参数调整数据规模和分布。</p>
                <p><strong>性能指标：</strong>主要关注插入速度、查询速度、内存使用和树结构效率。</p>
                <p><strong>可视化：</strong>部分测试提供实时可视化，帮助理解四叉树的工作原理。</p>
            </div>
        </div>
    </div>

    <script>
        // 快速测试函数
        function runViewportTest() {
            // 打开可视化测试页面并自动运行视口测试
            const win = window.open('quad_tree_visual.html', '_blank');
            win.addEventListener('load', () => {
                setTimeout(() => {
                    if (win.testViewportQuery) {
                        win.testViewportQuery();
                    }
                }, 1000);
            });
        }

        function runDynamicTest() {
            // 打开可视化测试页面并自动运行动态测试
            const win = window.open('quad_tree_visual.html', '_blank');
            win.addEventListener('load', () => {
                setTimeout(() => {
                    if (win.testDynamicOperations) {
                        win.testDynamicOperations();
                    }
                }, 1000);
            });
        }

        // 检查测试文件是否存在
        function checkTestFiles() {
            const testFiles = [
                'quad_tree.test.html',
                'quad_tree_visual.html', 
                'quad_tree_benchmark.html',
                'quad_tree.test.js'
            ];

            testFiles.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (!response.ok) {
                            console.warn(`测试文件 ${file} 不存在或无法访问`);
                        }
                    })
                    .catch(error => {
                        console.warn(`无法检查测试文件 ${file}:`, error);
                    });
            });
        }

        // 页面加载完成后检查测试文件
        window.addEventListener('load', checkTestFiles);

        // 添加一些动画效果
        function addAnimations() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        // 页面加载完成后添加动画
        window.addEventListener('load', () => {
            setTimeout(addAnimations, 100);
        });
    </script>
</body>
</html>

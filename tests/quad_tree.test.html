<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四叉树功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .performance {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007acc;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>四叉树功能测试</h1>
    
    <div class="test-container">
        <h2 class="test-title">测试控制面板</h2>
        <button onclick="runAllTests()">运行所有测试</button>
        <button onclick="runBasicTests()">基础功能测试</button>
        <button onclick="runPerformanceTests()">性能测试</button>
        <button onclick="runViewportTests()">视口搜索测试</button>
        <button onclick="runDynamicTests()">动态操作测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div class="test-container">
        <h2 class="test-title">测试统计</h2>
        <div class="stats" id="stats">
            <div class="stat-item">
                <div class="stat-value" id="totalTests">0</div>
                <div class="stat-label">总测试数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="passedTests">0</div>
                <div class="stat-label">通过测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedTests">0</div>
                <div class="stat-label">失败测试</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="testTime">0ms</div>
                <div class="stat-label">总耗时</div>
            </div>
        </div>
    </div>

    <div id="results"></div>

    <!-- 内联四叉树实现 -->
    <script>
        // 内联四叉树实现 - 直接基于TypeScript源码
        class QuadTree {
            constructor(bounds, maxNodes = 10, maxDepth = 5, depth = 0) {
                this.bounds = bounds;
                this.nodes = [];
                this.children = [];
                this.maxNodes = maxNodes;
                this.maxDepth = maxDepth;
                this.depth = depth;
                this.divided = false;
            }

            containsBounds(bounds) {
                return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
                       bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
            }

            intersects(range) {
                const c1 = range.maxX < this.bounds.minX;
                const c2 = range.minX > this.bounds.maxX;
                const c3 = range.maxY < this.bounds.minY;
                const c4 = range.minY > this.bounds.maxY;

                const orResult = c1 || c2 || c3 || c4;
                const result = !orResult;

                return result;
            }

            boundsIntersect(bounds1, bounds2) {
                return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                         bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
            }

            subdivide() {
                if (this.divided || this.depth >= this.maxDepth) return;

                const { minX, minY, maxX, maxY } = this.bounds;
                const midX = (minX + maxX) / 2;
                const midY = (minY + maxY) / 2;

                this.children = [
                    new QuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
                ];

                this.divided = true;

                const nodesToRedistribute = [...this.nodes];
                this.nodes = [];

                for (const node of nodesToRedistribute) {
                    this.insert(node);
                }
            }

            insert(node) {
                if (!this.containsBounds(node.bounds)) {
                    return false;
                }

                if (this.nodes.length < this.maxNodes && !this.divided) {
                    this.nodes.push(node);
                    return true;
                }

                if (!this.divided) {
                    this.subdivide();
                }

                for (const child of this.children) {
                    if (child.insert(node)) {
                        return true;
                    }
                }

                this.nodes.push(node);
                return true;
            }

            query(range, found = []) {
                if (!this.intersects(range)) {
                    return found;
                }

                for (const node of this.nodes) {
                    if (this.boundsIntersect(range, node.bounds)) {
                        found.push(node);
                    }
                }

                if (this.divided) {
                    for (const child of this.children) {
                        child.query(range, found);
                    }
                }

                return found;
            }

            remove(nodeId) {
                const index = this.nodes.findIndex(node => node.id === nodeId);
                if (index !== -1) {
                    this.nodes.splice(index, 1);
                    return true;
                }

                if (this.divided) {
                    for (const child of this.children) {
                        if (child.remove(nodeId)) {
                            return true;
                        }
                    }
                }

                return false;
            }

            update(nodeId, newBounds) {
                if (this.remove(nodeId)) {
                    return this.insert({ id: nodeId, bounds: newBounds });
                }
                return false;
            }

            clear() {
                this.nodes = [];
                this.children = [];
                this.divided = false;
            }

            getStats() {
                let totalNodes = this.nodes.length;
                let maxDepth = this.depth;
                let leafNodes = this.divided ? 0 : 1;

                if (this.divided) {
                    for (const child of this.children) {
                        const childStats = child.getStats();
                        totalNodes += childStats.totalNodes;
                        maxDepth = Math.max(maxDepth, childStats.depth);
                        leafNodes += childStats.leafNodes;
                    }
                }

                return { totalNodes, depth: maxDepth, leafNodes };
            }
        }

        function createQuadTree(bounds, maxNodes = 10, maxDepth = 5) {
            return new QuadTree(bounds, maxNodes, maxDepth);
        }

        // 其他辅助函数
        function multiplyMatrix(a, b) {
            return [
                a[0] * b[0] + a[2] * b[1],
                a[1] * b[0] + a[3] * b[1],
                a[0] * b[2] + a[2] * b[3],
                a[1] * b[2] + a[3] * b[3],
                a[0] * b[4] + a[2] * b[5] + a[4],
                a[1] * b[4] + a[3] * b[5] + a[5]
            ];
        }

        function getNodeWorldBounds(node, parentMatrix) {
            if (!node.glData) return null;

            const currentMatrix = node.mat ? multiplyMatrix(parentMatrix, node.mat) : parentMatrix;
            const { vertex } = node.glData;

            let minX = Infinity, minY = Infinity;
            let maxX = -Infinity, maxY = -Infinity;

            for (let i = 0; i < vertex.length; i += 2) {
                const worldX = currentMatrix[0] * vertex[i] + currentMatrix[2] * vertex[i + 1] + currentMatrix[4];
                const worldY = currentMatrix[1] * vertex[i] + currentMatrix[3] * vertex[i + 1] + currentMatrix[5];

                minX = Math.min(minX, worldX);
                minY = Math.min(minY, worldY);
                maxX = Math.max(maxX, worldX);
                maxY = Math.max(maxY, worldY);
            }

            return { minX, minY, maxX, maxY };
        }

        function buildSpatialIndex(tree, manager, parentMatrix = [1, 0, 0, 1, 0, 0]) {
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

            function calculateBounds(node, matrix) {
                const bounds = getNodeWorldBounds(node, matrix);
                if (bounds) {
                    minX = Math.min(minX, bounds.minX);
                    minY = Math.min(minY, bounds.minY);
                    maxX = Math.max(maxX, bounds.maxX);
                    maxY = Math.max(maxY, bounds.maxY);
                }

                if (node.children) {
                    const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;
                    node.children.forEach(child => calculateBounds(child, currentMatrix));
                }
            }

            calculateBounds(tree, parentMatrix);

            const padding = 100;
            const worldBounds = {
                minX: minX - padding,
                minY: minY - padding,
                maxX: maxX + padding,
                maxY: maxY + padding
            };

            const spatialIndex = createQuadTree(worldBounds, 10, 6);

            function insertNodes(node, matrix) {
                const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;

                if (node.glData && node.id) {
                    const bounds = getNodeWorldBounds(node, matrix);
                    if (bounds) {
                        spatialIndex.insert({ id: node.id, bounds });
                    }
                }

                if (node.children) {
                    node.children.forEach(child => insertNodes(child, currentMatrix));
                }
            }

            insertNodes(tree, parentMatrix);
            return spatialIndex;
        }

        function getViewportFromRoot(rootMat, canvasWidth, canvasHeight) {
            const scaleX = rootMat[0];
            const scaleY = rootMat[3];
            const translateX = rootMat[4];
            const translateY = rootMat[5];

            return {
                x: -translateX / scaleX,
                y: -translateY / scaleY,
                width: canvasWidth / scaleX,
                height: canvasHeight / scaleY,
                scale: scaleX
            };
        }
    </script>
    <script>

        // 测试统计
        let testStats = {
            total: 0,
            passed: 0,
            failed: 0,
            startTime: 0
        };

        // 更新统计显示
        function updateStats() {
            document.getElementById('totalTests').textContent = testStats.total;
            document.getElementById('passedTests').textContent = testStats.passed;
            document.getElementById('failedTests').textContent = testStats.failed;
            document.getElementById('testTime').textContent = 
                testStats.startTime ? `${Date.now() - testStats.startTime}ms` : '0ms';
        }

        // 添加测试结果
        function addResult(title, success, message, details = '') {
            testStats.total++;
            if (success) {
                testStats.passed++;
            } else {
                testStats.failed++;
            }
            updateStats();

            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = 'test-container';
            
            resultDiv.innerHTML = `
                <h3 class="test-title">${title}</h3>
                <div class="test-result ${success ? 'success' : 'error'}">
                    ${message}
                </div>
                ${details ? `<div class="test-result info">${details}</div>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        // 清空结果
        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
            testStats = { total: 0, passed: 0, failed: 0, startTime: 0 };
            updateStats();
        };

        // 创建测试节点
        function createTestNode(id, x, y, width = 100, height = 100, color = [255, 0, 0, 255]) {
            return {
                id,
                mat: [1, 0, 0, 1, x, y],
                glData: {
                    vertex: [-width/2, -height/2, width/2, -height/2, width/2, height/2, -width/2, height/2],
                    rgba: color
                }
            };
        }

        // 创建测试树结构
        function createTestTree() {
            const root = {
                id: 'root',
                mat: [1, 0, 0, 1, 0, 0],
                children: [
                    createTestNode('node1', 100, 100),
                    createTestNode('node2', 300, 200),
                    createTestNode('node3', -100, -100),
                    createTestNode('node4', 500, 300),
                    {
                        id: 'group1',
                        mat: [1, 0, 0, 1, 200, 200],
                        children: [
                            createTestNode('child1', 50, 50),
                            createTestNode('child2', -50, -50)
                        ]
                    }
                ]
            };
            return root;
        }

        // 基础功能测试
        window.runBasicTests = function() {
            testStats.startTime = Date.now();

            // 启用调试模式
            window.DEBUG_QUADTREE = true;

            try {
                // 测试1: 创建四叉树
                const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
                const quadTree = createQuadTree(bounds, 5, 4);
                
                addResult(
                    '测试1: 创建四叉树', 
                    true, 
                    '✓ 四叉树创建成功',
                    `边界: ${JSON.stringify(bounds)}, 最大节点数: 5, 最大深度: 4`
                );

                // 测试2: 插入节点
                const testNodes = [
                    { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                    { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                    { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
                ];

                let insertSuccess = true;
                let insertResults = [];
                testNodes.forEach(node => {
                    const result = quadTree.insert(node);
                    insertResults.push({ id: node.id, success: result });
                    if (!result) {
                        insertSuccess = false;
                    }
                });

                addResult(
                    '测试2: 插入节点',
                    insertSuccess,
                    insertSuccess ? '✓ 所有节点插入成功' : '✗ 部分节点插入失败',
                    `插入结果: ${insertResults.map(r => `${r.id}: ${r.success}`).join(', ')}`
                );

                // 测试3: 查询节点
                // 先查询整个树看看有什么节点
                const allNodesInTree = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
                console.log('树中所有节点:', allNodesInTree);

                const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
                const found = quadTree.query(queryRange);

                // 调试信息：检查插入的节点和查询结果
                console.log('插入的节点:', testNodes);
                console.log('查询范围:', queryRange);
                console.log('查询结果:', found);
                console.log('四叉树统计:', quadTree.getStats());

                // 手动验证相交逻辑
                let expectedCount = 0;
                testNodes.forEach(node => {
                    const intersects = !(queryRange.maxX < node.bounds.minX || queryRange.minX > node.bounds.maxX ||
                                        queryRange.maxY < node.bounds.minY || queryRange.minY > node.bounds.minY);
                    console.log(`节点 ${node.id} 与查询范围相交:`, intersects, node.bounds);
                    if (intersects) expectedCount++;
                });

                addResult(
                    '测试3: 查询节点',
                    found.length === expectedCount,
                    `查询到 ${found.length} 个节点，期望 ${expectedCount} 个`,
                    `查询范围: ${JSON.stringify(queryRange)}\n找到节点: ${found.map(n => n.id).join(', ')}\n树中所有节点: ${allNodesInTree.map(n => n.id).join(', ')}`
                );

            } catch (error) {
                addResult('基础功能测试', false, `✗ 测试失败: ${error.message}`);
            } finally {
                // 关闭调试模式
                window.DEBUG_QUADTREE = false;
            }
        };

        // 性能测试
        window.runPerformanceTests = function() {
            testStats.startTime = Date.now();
            
            try {
                const bounds = { minX: -5000, minY: -5000, maxX: 5000, maxY: 5000 };
                const quadTree = createQuadTree(bounds, 10, 6);
                
                // 生成大量测试数据
                const nodeCount = 10000;
                const nodes = [];
                
                const insertStart = performance.now();
                for (let i = 0; i < nodeCount; i++) {
                    const x = Math.random() * 10000 - 5000;
                    const y = Math.random() * 10000 - 5000;
                    const size = Math.random() * 100 + 50;
                    
                    const node = {
                        id: `perf_node_${i}`,
                        bounds: {
                            minX: x - size/2,
                            minY: y - size/2,
                            maxX: x + size/2,
                            maxY: y + size/2
                        }
                    };
                    
                    nodes.push(node);
                    quadTree.insert(node);
                }
                const insertTime = performance.now() - insertStart;
                
                addResult(
                    '性能测试: 大量插入',
                    true,
                    `✓ 插入 ${nodeCount} 个节点`,
                    `耗时: ${insertTime.toFixed(2)}ms, 平均: ${(insertTime/nodeCount).toFixed(4)}ms/节点`
                );

                // 查询性能测试
                const queryStart = performance.now();
                const queryCount = 1000;
                let totalFound = 0;
                
                for (let i = 0; i < queryCount; i++) {
                    const x = Math.random() * 8000 - 4000;
                    const y = Math.random() * 8000 - 4000;
                    const size = Math.random() * 1000 + 500;
                    
                    const queryRange = {
                        minX: x - size/2,
                        minY: y - size/2,
                        maxX: x + size/2,
                        maxY: y + size/2
                    };
                    
                    const found = quadTree.query(queryRange);
                    totalFound += found.length;
                }
                const queryTime = performance.now() - queryStart;
                
                addResult(
                    '性能测试: 大量查询',
                    true,
                    `✓ 执行 ${queryCount} 次查询`,
                    `总耗时: ${queryTime.toFixed(2)}ms, 平均: ${(queryTime/queryCount).toFixed(4)}ms/查询, 平均找到: ${(totalFound/queryCount).toFixed(1)} 个节点`
                );

                // 获取统计信息
                const stats = quadTree.getStats();
                addResult(
                    '性能测试: 树结构统计',
                    true,
                    '✓ 统计信息获取成功',
                    `总节点数: ${stats.totalNodes}, 最大深度: ${stats.depth}, 叶子节点数: ${stats.leafNodes}`
                );

            } catch (error) {
                addResult('性能测试', false, `✗ 测试失败: ${error.message}`);
            }
        };

        // 视口搜索测试
        window.runViewportTests = function() {
            try {
                // 创建测试树
                const testTree = createTestTree();

                // 模拟GLBufferManager
                const mockManager = {
                    nodeCache: new Map(),
                    spatialIndex: null,
                    bufferData: null,
                    needsRebuild: true,
                    lastUpdateTime: 0
                };

                // 构建空间索引
                const spatialIndex = buildSpatialIndex(testTree, mockManager);

                addResult(
                    '视口测试1: 构建空间索引',
                    spatialIndex !== null,
                    '✓ 空间索引构建成功',
                    `索引统计: ${JSON.stringify(spatialIndex.getStats())}`
                );

                // 测试不同视口的查询
                const viewports = [
                    { x: 0, y: 0, width: 200, height: 200, name: '中心视口' },
                    { x: 200, y: 200, width: 300, height: 300, name: '右下视口' },
                    { x: -200, y: -200, width: 200, height: 200, name: '左上视口' },
                    { x: 400, y: 400, width: 200, height: 200, name: '远端视口' }
                ];

                viewports.forEach((viewport, index) => {
                    const viewportBounds = {
                        minX: viewport.x,
                        minY: viewport.y,
                        maxX: viewport.x + viewport.width,
                        maxY: viewport.y + viewport.height
                    };

                    const visibleNodes = spatialIndex.query(viewportBounds);

                    addResult(
                        `视口测试${index + 2}: ${viewport.name}`,
                        true,
                        `✓ 找到 ${visibleNodes.length} 个可见节点`,
                        `视口: ${JSON.stringify(viewport)}, 节点: ${visibleNodes.map(n => n.id).join(', ')}`
                    );
                });

                // 测试矩阵变换后的视口计算
                const rootMatrix = [2, 0, 0, 2, 100, 100]; // 2x缩放 + 平移
                const canvasWidth = 800;
                const canvasHeight = 600;

                const viewport = getViewportFromRoot(rootMatrix, canvasWidth, canvasHeight);

                addResult(
                    '视口测试6: 矩阵变换视口计算',
                    viewport !== null,
                    '✓ 视口计算成功',
                    `根矩阵: [${rootMatrix.join(', ')}], 计算视口: ${JSON.stringify(viewport)}`
                );

            } catch (error) {
                addResult('视口搜索测试', false, `✗ 测试失败: ${error.message}`);
            }
        };

        // 动态操作测试
        window.runDynamicTests = function() {
            try {
                const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
                const quadTree = createQuadTree(bounds, 5, 4);

                // 初始插入一些节点
                const initialNodes = [
                    { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                    { id: 'dynamic2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                    { id: 'dynamic3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
                ];

                initialNodes.forEach(node => quadTree.insert(node));

                addResult(
                    '动态测试1: 初始插入',
                    true,
                    `✓ 插入 ${initialNodes.length} 个初始节点`,
                    `节点: ${initialNodes.map(n => n.id).join(', ')}`
                );

                // 测试动态添加
                const newNodes = [
                    { id: 'new1', bounds: { minX: 400, minY: 400, maxX: 500, maxY: 500 } },
                    { id: 'new2', bounds: { minX: -300, minY: -300, maxX: -200, maxY: -200 } }
                ];

                let addSuccess = true;
                newNodes.forEach(node => {
                    if (!quadTree.insert(node)) {
                        addSuccess = false;
                    }
                });

                addResult(
                    '动态测试2: 动态添加节点',
                    addSuccess,
                    addSuccess ? `✓ 成功添加 ${newNodes.length} 个新节点` : '✗ 添加节点失败',
                    `新节点: ${newNodes.map(n => n.id).join(', ')}`
                );

                // 测试查询验证添加结果
                const queryAll = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
                const expectedCount = initialNodes.length + newNodes.length;

                addResult(
                    '动态测试3: 验证添加结果',
                    queryAll.length === expectedCount,
                    `✓ 查询到 ${queryAll.length} 个节点，期望 ${expectedCount} 个`,
                    `所有节点: ${queryAll.map(n => n.id).join(', ')}`
                );

                // 测试动态删除
                const deleteIds = ['dynamic2', 'new1'];
                let deleteSuccess = true;
                deleteIds.forEach(id => {
                    if (!quadTree.remove(id)) {
                        deleteSuccess = false;
                    }
                });

                addResult(
                    '动态测试4: 动态删除节点',
                    deleteSuccess,
                    deleteSuccess ? `✓ 成功删除 ${deleteIds.length} 个节点` : '✗ 删除节点失败',
                    `删除节点: ${deleteIds.join(', ')}`
                );

                // 验证删除结果
                const queryAfterDelete = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
                const expectedAfterDelete = expectedCount - deleteIds.length;

                addResult(
                    '动态测试5: 验证删除结果',
                    queryAfterDelete.length === expectedAfterDelete,
                    `✓ 删除后查询到 ${queryAfterDelete.length} 个节点，期望 ${expectedAfterDelete} 个`,
                    `剩余节点: ${queryAfterDelete.map(n => n.id).join(', ')}`
                );

                // 测试节点更新
                const updateSuccess = quadTree.update('dynamic1', { minX: 500, minY: 500, maxX: 600, maxY: 600 });

                addResult(
                    '动态测试6: 更新节点位置',
                    updateSuccess,
                    updateSuccess ? '✓ 节点位置更新成功' : '✗ 节点位置更新失败',
                    '将 dynamic1 从 (0,0,100,100) 移动到 (500,500,600,600)'
                );

                // 验证更新结果
                const queryUpdated = quadTree.query({ minX: 450, minY: 450, maxX: 650, maxY: 650 });
                const foundUpdated = queryUpdated.find(n => n.id === 'dynamic1');

                // 调试信息
                console.log('更新后查询范围:', { minX: 450, minY: 450, maxX: 650, maxY: 650 });
                console.log('更新后查询结果:', queryUpdated);
                console.log('期望的新边界:', { minX: 500, minY: 500, maxX: 600, maxY: 600 });

                // 查询整个树看看节点在哪里
                const allNodes = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
                console.log('所有节点:', allNodes);
                const dynamic1Node = allNodes.find(n => n.id === 'dynamic1');
                console.log('dynamic1节点:', dynamic1Node);

                addResult(
                    '动态测试7: 验证更新结果',
                    foundUpdated !== undefined,
                    foundUpdated ? '✓ 在新位置找到更新的节点' : '✗ 未在新位置找到更新的节点',
                    `查询范围 (450,450,650,650) 找到: ${queryUpdated.map(n => n.id).join(', ')}`
                );

                // 最终统计
                const finalStats = quadTree.getStats();
                addResult(
                    '动态测试8: 最终统计',
                    true,
                    '✓ 动态操作完成',
                    `最终统计: 总节点 ${finalStats.totalNodes}, 深度 ${finalStats.depth}, 叶子节点 ${finalStats.leafNodes}`
                );

            } catch (error) {
                addResult('动态操作测试', false, `✗ 测试失败: ${error.message}`);
            }
        };

        // 运行所有测试
        window.runAllTests = function() {
            clearResults();
            testStats.startTime = Date.now();

            setTimeout(() => runBasicTests(), 100);
            setTimeout(() => runPerformanceTests(), 500);
            setTimeout(() => runViewportTests(), 1000);
            setTimeout(() => runDynamicTests(), 1500);
        };

        // 初始化
        updateStats();
    </script>
</body>
</html>

describe('Proxy Tests', () => {
  let target, proxy;
  
  beforeEach(() => {
    target = {
      name: 'Target',
      value: 42,
      greet() {
        return `Hello, ${this.name}`;
      }
    };
    
    const handler = {
      get(target, prop, receiver) {
        console.log(`Getting property "${prop}"`);
        return Reflect.get(...arguments);
      },
      set(target, prop, value, receiver) {
        console.log(`Setting property "${prop}" to "${value}"`);
        return Reflect.set(...arguments);
      },
      apply(target, thisArg, argumentsList) {
        console.log(`Calling method "${target.name}"`);
        return Reflect.apply(...arguments);
      }
    };
    
    proxy = new Proxy(target, handler);
  });

  it('should intercept property access', () => {
    expect(proxy.value).toBe(42);
  });

  it('should intercept property assignment', () => {
    proxy.value = 100;
    expect(proxy.value).toBe(100);
  });

  it('should intercept method calls', () => {
    expect(proxy.greet()).toBe('Hello, Target');
  });

  it('should maintain this binding', () => {
    const greeting = proxy.greet;
    expect(greeting()).toBe('Hello, Target');
  });
});

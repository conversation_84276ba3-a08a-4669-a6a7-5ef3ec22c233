<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终验证测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        h2 { color: #333; border-bottom: 2px solid #007acc; padding-bottom: 10px; }
    </style>
</head>
<body>
    <h1>最终验证测试</h1>
    <button onclick="runFinalTest()">运行最终验证</button>
    <div id="results"></div>

    <script>
        // 内联四叉树实现 - 与测试文件完全一致
        class QuadTree {
            constructor(bounds, maxNodes = 10, maxDepth = 5, depth = 0) {
                this.bounds = bounds;
                this.nodes = [];
                this.children = [];
                this.maxNodes = maxNodes;
                this.maxDepth = maxDepth;
                this.depth = depth;
                this.divided = false;
            }

            containsBounds(bounds) {
                return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
                       bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
            }

            intersects(range) {
                return !(range.maxX < this.bounds.minX || range.minX > this.bounds.maxX ||
                         range.maxY < this.bounds.minY || range.minY > this.bounds.minY);
            }

            boundsIntersect(bounds1, bounds2) {
                return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                         bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
            }

            subdivide() {
                if (this.divided || this.depth >= this.maxDepth) return;

                const { minX, minY, maxX, maxY } = this.bounds;
                const midX = (minX + maxX) / 2;
                const midY = (minY + maxY) / 2;

                this.children = [
                    new QuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new QuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
                ];

                this.divided = true;

                const nodesToRedistribute = [...this.nodes];
                this.nodes = [];

                for (const node of nodesToRedistribute) {
                    this.insert(node);
                }
            }

            insert(node) {
                if (!this.containsBounds(node.bounds)) {
                    return false;
                }

                if (this.nodes.length < this.maxNodes && !this.divided) {
                    this.nodes.push(node);
                    return true;
                }

                if (!this.divided) {
                    this.subdivide();
                }

                for (const child of this.children) {
                    if (child.insert(node)) {
                        return true;
                    }
                }

                this.nodes.push(node);
                return true;
            }

            query(range, found = []) {
                if (!this.intersects(range)) {
                    return found;
                }

                for (const node of this.nodes) {
                    if (this.boundsIntersect(range, node.bounds)) {
                        found.push(node);
                    }
                }

                if (this.divided) {
                    for (const child of this.children) {
                        child.query(range, found);
                    }
                }

                return found;
            }

            remove(nodeId) {
                const index = this.nodes.findIndex(node => node.id === nodeId);
                if (index !== -1) {
                    this.nodes.splice(index, 1);
                    return true;
                }

                if (this.divided) {
                    for (const child of this.children) {
                        if (child.remove(nodeId)) {
                            return true;
                        }
                    }
                }

                return false;
            }

            update(nodeId, newBounds) {
                if (this.remove(nodeId)) {
                    return this.insert({ id: nodeId, bounds: newBounds });
                }
                return false;
            }

            getStats() {
                let totalNodes = this.nodes.length;
                let maxDepth = this.depth;
                let leafNodes = this.divided ? 0 : 1;

                if (this.divided) {
                    for (const child of this.children) {
                        const childStats = child.getStats();
                        totalNodes += childStats.totalNodes;
                        maxDepth = Math.max(maxDepth, childStats.depth);
                        leafNodes += childStats.leafNodes;
                    }
                }

                return { totalNodes, depth: maxDepth, leafNodes };
            }
        }

        function createQuadTree(bounds, maxNodes = 10, maxDepth = 5) {
            return new QuadTree(bounds, maxNodes, maxDepth);
        }

        function addResult(title, success, message, details = '') {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${title}:</strong> ${success ? '✓' : '✗'} ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            document.getElementById('results').appendChild(div);
        }

        function addSection(title) {
            const div = document.createElement('div');
            div.innerHTML = `<h2>${title}</h2>`;
            document.getElementById('results').appendChild(div);
        }

        function runFinalTest() {
            document.getElementById('results').innerHTML = '';
            
            addSection('原始问题验证');
            
            // 测试1: 重现原始查询问题
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);
            
            const testNodes = [
                { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
            ];

            // 插入节点
            let insertSuccess = true;
            testNodes.forEach(node => {
                if (!quadTree.insert(node)) {
                    insertSuccess = false;
                }
            });

            addResult('插入节点', insertSuccess, `插入了 ${testNodes.length} 个节点`);

            // 原始问题查询
            const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            const found = quadTree.query(queryRange);
            
            // 手动验证期望结果
            let expectedCount = 0;
            testNodes.forEach(node => {
                const intersects = !(queryRange.maxX < node.bounds.minX || queryRange.minX > node.bounds.maxX ||
                                    queryRange.maxY < node.bounds.minY || queryRange.minY > node.bounds.minY);
                if (intersects) expectedCount++;
            });

            addResult('测试3: 查询节点', found.length === expectedCount, 
                     `查询到 ${found.length} 个节点，期望 ${expectedCount} 个`,
                     `查询范围: ${JSON.stringify(queryRange)}, 找到: ${found.map(n => n.id).join(', ')}`);

            // 测试2: 重现原始更新问题
            addSection('更新操作验证');
            
            const quadTree2 = createQuadTree(bounds, 5, 4);
            
            const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
            quadTree2.insert(initialNode);

            // 更新节点位置
            const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
            const updateResult = quadTree2.update('dynamic1', newBounds);

            addResult('更新节点', updateResult, `更新${updateResult ? '成功' : '失败'}`);

            // 验证更新后位置
            const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
            const foundAfterUpdate = quadTree2.query(newQueryRange);
            const foundUpdated = foundAfterUpdate.find(n => n.id === 'dynamic1');

            addResult('动态测试7: 验证更新结果', foundUpdated !== undefined, 
                     `在新位置${foundUpdated ? '找到' : '未找到'}节点`,
                     `查询范围: ${JSON.stringify(newQueryRange)}, 找到: ${foundAfterUpdate.map(n => n.id).join(', ')}`);

            // 验证旧位置没有节点
            const oldQueryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            const foundInOldPos = quadTree2.query(oldQueryRange);
            const stillInOldPos = foundInOldPos.find(n => n.id === 'dynamic1');

            addResult('验证旧位置清理', stillInOldPos === undefined, 
                     `在旧位置${stillInOldPos ? '仍然找到' : '未找到'}节点`);

            // 额外验证测试
            addSection('额外验证测试');
            
            // 测试复杂场景
            const quadTree3 = createQuadTree(bounds, 3, 3);
            
            // 插入多个节点触发分割
            const manyNodes = [];
            for (let i = 0; i < 10; i++) {
                const x = (i % 3) * 200;
                const y = Math.floor(i / 3) * 200;
                manyNodes.push({
                    id: `node${i}`,
                    bounds: { minX: x, minY: y, maxX: x + 50, maxY: y + 50 }
                });
            }

            let allInserted = true;
            manyNodes.forEach(node => {
                if (!quadTree3.insert(node)) {
                    allInserted = false;
                }
            });

            addResult('批量插入', allInserted, `插入了 ${manyNodes.length} 个节点`);

            // 查询所有节点
            const allFound = quadTree3.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            addResult('查询所有节点', allFound.length === manyNodes.length, 
                     `查询到 ${allFound.length} 个节点，期望 ${manyNodes.length} 个`);

            // 删除一些节点
            const toDelete = ['node1', 'node3', 'node5'];
            let deleteSuccess = true;
            toDelete.forEach(id => {
                if (!quadTree3.remove(id)) {
                    deleteSuccess = false;
                }
            });

            addResult('批量删除', deleteSuccess, `删除了 ${toDelete.length} 个节点`);

            // 验证删除结果
            const afterDelete = quadTree3.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            const expectedAfterDelete = manyNodes.length - toDelete.length;
            addResult('验证删除结果', afterDelete.length === expectedAfterDelete, 
                     `删除后剩余 ${afterDelete.length} 个节点，期望 ${expectedAfterDelete} 个`);

            // 最终总结
            addSection('测试总结');
            const allResults = document.querySelectorAll('.result.success, .result.error');
            const successCount = document.querySelectorAll('.result.success').length;
            const totalCount = allResults.length;
            
            addResult('总体结果', successCount === totalCount, 
                     `${successCount}/${totalCount} 个测试通过`,
                     successCount === totalCount ? '🎉 所有测试都通过了！' : '❌ 仍有测试失败');
        }
    </script>
</body>
</html>

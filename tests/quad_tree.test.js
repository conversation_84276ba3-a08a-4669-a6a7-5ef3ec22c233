// 四叉树测试脚本
// 这个文件可以在浏览器中直接运行，用于测试四叉树功能

// 模拟导入的类型定义
const Matrix = Array;

// 包围盒接口实现
class BoundingBox {
    constructor(minX, minY, maxX, maxY) {
        this.minX = minX;
        this.minY = minY;
        this.maxX = maxX;
        this.maxY = maxY;
    }
}

// 四叉树节点数据
class QuadTreeNode {
    constructor(id, bounds) {
        this.id = id;
        this.bounds = bounds;
    }
}

// 四叉树实现（简化版本用于测试）
class QuadTree {
    constructor(bounds, maxNodes = 10, maxDepth = 5, depth = 0) {
        this.bounds = bounds;
        this.nodes = [];
        this.children = [];
        this.maxNodes = maxNodes;
        this.maxDepth = maxDepth;
        this.depth = depth;
        this.divided = false;
    }

    // 检查包围盒是否包含另一个包围盒
    containsBounds(bounds) {
        return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
               bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
    }

    // 检查包围盒是否相交
    intersects(range) {
        return !(range.maxX < this.bounds.minX || range.minX > this.bounds.maxX ||
                 range.maxY < this.bounds.minY || range.minY > this.bounds.minY);
    }

    // 检查两个包围盒是否相交
    boundsIntersect(bounds1, bounds2) {
        return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                 bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
    }

    // 分割四叉树
    subdivide() {
        if (this.divided || this.depth >= this.maxDepth) return;

        const { minX, minY, maxX, maxY } = this.bounds;
        const midX = (minX + maxX) / 2;
        const midY = (minY + maxY) / 2;

        // 创建四个子象限
        this.children = [
            new QuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
            new QuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
        ];

        this.divided = true;

        // 将现有节点重新分配到子象限
        const nodesToRedistribute = [...this.nodes];
        this.nodes = [];

        for (const node of nodesToRedistribute) {
            this.insert(node);
        }
    }

    // 插入节点
    insert(node) {
        // 调试信息
        const contained = this.containsBounds(node.bounds);
        if (window.DEBUG_QUADTREE) {
            console.log(`插入节点 ${node.id}:`, {
                nodeBounds: node.bounds,
                treeBounds: this.bounds,
                contained: contained,
                depth: this.depth
            });
        }

        if (!contained) {
            return false;
        }

        if (this.nodes.length < this.maxNodes && !this.divided) {
            this.nodes.push(node);
            if (window.DEBUG_QUADTREE) {
                console.log(`节点 ${node.id} 插入到深度 ${this.depth}, 当前节点数: ${this.nodes.length}`);
            }
            return true;
        }

        if (!this.divided) {
            this.subdivide();
        }

        for (const child of this.children) {
            if (child.insert(node)) {
                return true;
            }
        }

        this.nodes.push(node);
        if (window.DEBUG_QUADTREE) {
            console.log(`节点 ${node.id} 插入到深度 ${this.depth} (无法插入子象限)`);
        }
        return true;
    }

    // 查询与指定范围相交的节点
    query(range, found = []) {
        const intersects = this.intersects(range);
        if (window.DEBUG_QUADTREE) {
            console.log(`查询深度 ${this.depth}:`, {
                queryRange: range,
                treeBounds: this.bounds,
                intersects: intersects,
                nodesCount: this.nodes.length,
                divided: this.divided
            });
        }

        if (!intersects) {
            return found;
        }

        for (const node of this.nodes) {
            const nodeIntersects = this.boundsIntersect(range, node.bounds);
            if (window.DEBUG_QUADTREE) {
                console.log(`检查节点 ${node.id}:`, {
                    nodeBounds: node.bounds,
                    intersects: nodeIntersects
                });
            }
            if (nodeIntersects) {
                found.push(node);
            }
        }

        if (this.divided) {
            for (const child of this.children) {
                child.query(range, found);
            }
        }

        return found;
    }

    // 删除节点
    remove(nodeId) {
        // 在当前层级查找并删除
        const index = this.nodes.findIndex(node => node.id === nodeId);
        if (index !== -1) {
            this.nodes.splice(index, 1);
            if (window.DEBUG_QUADTREE) {
                console.log(`在深度 ${this.depth} 删除节点 ${nodeId}`);
            }
            return true;
        }

        // 在子象限中查找并删除
        if (this.divided) {
            for (const child of this.children) {
                if (child.remove(nodeId)) {
                    return true;
                }
            }
        }

        return false;
    }

    // 更新节点位置
    update(nodeId, newBounds) {
        if (window.DEBUG_QUADTREE) {
            console.log(`更新节点 ${nodeId} 到新边界:`, newBounds);
        }

        // 先删除旧节点
        const removeResult = this.remove(nodeId);
        if (window.DEBUG_QUADTREE) {
            console.log(`删除节点 ${nodeId} 结果: ${removeResult}`);
        }

        if (removeResult) {
            // 重新插入新位置
            const newNode = { id: nodeId, bounds: newBounds };
            const insertResult = this.insert(newNode);
            if (window.DEBUG_QUADTREE) {
                console.log(`重新插入节点 ${nodeId} 结果: ${insertResult}`);
            }
            return insertResult;
        }
        return false;
    }

    // 清空四叉树
    clear() {
        this.nodes = [];
        this.children = [];
        this.divided = false;
    }

    // 获取统计信息
    getStats() {
        let totalNodes = this.nodes.length;
        let maxDepth = this.depth;
        let leafNodes = this.divided ? 0 : 1;

        if (this.divided) {
            for (const child of this.children) {
                const childStats = child.getStats();
                totalNodes += childStats.totalNodes;
                maxDepth = Math.max(maxDepth, childStats.depth);
                leafNodes += childStats.leafNodes;
            }
        }

        return { totalNodes, depth: maxDepth, leafNodes };
    }
}

// 创建四叉树的工厂函数
function createQuadTree(bounds, maxNodes = 10, maxDepth = 5) {
    return new QuadTree(bounds, maxNodes, maxDepth);
}

// 矩阵乘法
function multiplyMatrix(a, b) {
    return [
        a[0] * b[0] + a[2] * b[1],
        a[1] * b[0] + a[3] * b[1],
        a[0] * b[2] + a[2] * b[3],
        a[1] * b[2] + a[3] * b[3],
        a[0] * b[4] + a[2] * b[5] + a[4],
        a[1] * b[4] + a[3] * b[5] + a[5]
    ];
}

// 计算节点的世界坐标包围盒
function getNodeWorldBounds(node, parentMatrix) {
    if (!node.glData) return null;
    
    const currentMatrix = node.mat ? multiplyMatrix(parentMatrix, node.mat) : parentMatrix;
    const { vertex } = node.glData;
    
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    for (let i = 0; i < vertex.length; i += 2) {
        const worldX = currentMatrix[0] * vertex[i] + currentMatrix[2] * vertex[i + 1] + currentMatrix[4];
        const worldY = currentMatrix[1] * vertex[i] + currentMatrix[3] * vertex[i + 1] + currentMatrix[5];
        
        minX = Math.min(minX, worldX);
        minY = Math.min(minY, worldY);
        maxX = Math.max(maxX, worldX);
        maxY = Math.max(maxY, worldY);
    }
    
    return { minX, minY, maxX, maxY };
}

// 构建空间索引
function buildSpatialIndex(tree, manager, parentMatrix = [1, 0, 0, 1, 0, 0]) {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    // 计算世界边界
    function calculateBounds(node, matrix) {
        const bounds = getNodeWorldBounds(node, matrix);
        if (bounds) {
            minX = Math.min(minX, bounds.minX);
            minY = Math.min(minY, bounds.minY);
            maxX = Math.max(maxX, bounds.maxX);
            maxY = Math.max(maxY, bounds.maxY);
        }

        if (node.children) {
            const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;
            node.children.forEach(child => calculateBounds(child, currentMatrix));
        }
    }

    calculateBounds(tree, parentMatrix);

    // 扩展边界
    const padding = 100;
    const worldBounds = {
        minX: minX - padding,
        minY: minY - padding,
        maxX: maxX + padding,
        maxY: maxY + padding
    };

    const spatialIndex = createQuadTree(worldBounds, 10, 6);

    // 插入节点到四叉树
    function insertNodes(node, matrix) {
        const currentMatrix = node.mat ? multiplyMatrix(matrix, node.mat) : matrix;

        if (node.glData && node.id) {
            const bounds = getNodeWorldBounds(node, matrix);
            if (bounds) {
                spatialIndex.insert({ id: node.id, bounds });
            }
        }

        if (node.children) {
            node.children.forEach(child => insertNodes(child, currentMatrix));
        }
    }

    insertNodes(tree, parentMatrix);
    return spatialIndex;
}

// 根据root的mat和画布尺寸计算视口信息
function getViewportFromRoot(rootMat, canvasWidth, canvasHeight) {
    const scaleX = rootMat[0];
    const scaleY = rootMat[3];
    const translateX = rootMat[4];
    const translateY = rootMat[5];
    
    return {
        x: -translateX / scaleX,
        y: -translateY / scaleY,
        width: canvasWidth / scaleX,
        height: canvasHeight / scaleY,
        scale: scaleX
    };
}

// 导出给HTML使用
if (typeof window !== 'undefined') {
    window.QuadTree = QuadTree;
    window.createQuadTree = createQuadTree;
    window.buildSpatialIndex = buildSpatialIndex;
    window.getNodeWorldBounds = getNodeWorldBounds;
    window.getViewportFromRoot = getViewportFromRoot;
    window.multiplyMatrix = multiplyMatrix;
}

// 验证HTML修复的Node.js脚本
// 这个脚本模拟HTML中的测试逻辑

// 导入修复后的四叉树
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 读取并执行quad_tree_fixed.js
const quadTreeCode = fs.readFileSync(path.join(__dirname, 'quad_tree_fixed.js'), 'utf8');

// 创建一个模拟的window对象
global.window = {};

// 执行四叉树代码
eval(quadTreeCode);

// 获取导出的函数
const { QuadTree, createQuadTree } = global.window;

console.log('🧪 验证HTML修复效果\n');

// 测试1: 重现HTML中的测试3问题
console.log('--- 测试3: 查询节点 ---');
const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
const quadTree = createQuadTree(bounds, 5, 4);

const testNodes = [
    { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
    { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
    { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
];

// 插入节点
let insertSuccess = true;
testNodes.forEach(node => {
    if (!quadTree.insert(node)) {
        insertSuccess = false;
    }
});

console.log(`插入节点: ${insertSuccess ? '✅ 成功' : '❌ 失败'}`);

// 查询节点
const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
const found = quadTree.query(queryRange);

// 手动验证期望结果
let expectedCount = 0;
console.log('手动验证相交逻辑:');
testNodes.forEach(node => {
    const c1 = queryRange.maxX < node.bounds.minX;
    const c2 = queryRange.minX > node.bounds.maxX;
    const c3 = queryRange.maxY < node.bounds.minY;
    const c4 = queryRange.minY > node.bounds.maxY;
    const intersects = !(c1 || c2 || c3 || c4);

    console.log(`  节点 ${node.id}: ${JSON.stringify(node.bounds)} -> 相交: ${intersects}`);
    if (intersects) expectedCount++;
});

const querySuccess = found.length === expectedCount;
console.log(`查询节点: ${querySuccess ? '✅' : '❌'} 查询到 ${found.length} 个节点，期望 ${expectedCount} 个`);

// 测试2: 重现HTML中的动态测试7问题
console.log('\n--- 动态测试7: 验证更新结果 ---');
const quadTree2 = createQuadTree(bounds, 5, 4);

const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
quadTree2.insert(initialNode);

// 更新节点位置
const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
const updateResult = quadTree2.update('dynamic1', newBounds);

console.log(`更新节点: ${updateResult ? '✅ 成功' : '❌ 失败'}`);

// 验证更新后位置
const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
const foundAfterUpdate = quadTree2.query(newQueryRange);
const foundUpdated = foundAfterUpdate.find(n => n.id === 'dynamic1');

const updateVerifySuccess = foundUpdated !== undefined;
console.log(`验证更新结果: ${updateVerifySuccess ? '✅' : '❌'} 在新位置${foundUpdated ? '找到' : '未找到'}节点`);

// 验证旧位置没有节点
const oldQueryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
const foundInOldPos = quadTree2.query(oldQueryRange);
const stillInOldPos = foundInOldPos.find(n => n.id === 'dynamic1');

const oldPosCleanSuccess = stillInOldPos === undefined;
console.log(`验证旧位置清理: ${oldPosCleanSuccess ? '✅' : '❌'} 在旧位置${stillInOldPos ? '仍然找到' : '未找到'}节点`);

// 总结
console.log('\n📊 HTML修复验证总结:');
const allSuccess = insertSuccess && querySuccess && updateResult && updateVerifySuccess && oldPosCleanSuccess;
console.log(`总体结果: ${allSuccess ? '🎉 所有测试都通过了！' : '❌ 仍有测试失败'}`);

if (allSuccess) {
    console.log('✅ HTML中的四叉树问题已完全修复！');
} else {
    console.log('❌ HTML中仍存在问题，需要进一步调试');
}

# 四叉树测试套件

这是一个全面的四叉树功能和性能测试套件，用于验证四叉树在各种场景下的表现。

## 📁 文件结构

```
tests/
├── index.html              # 测试套件主页面
├── quad_tree.test.html      # 功能测试页面 (已修复)
├── quad_tree_visual.html    # 可视化测试页面
├── quad_tree_benchmark.html # 性能基准测试页面
├── quad_tree_fixed.js       # 修复版四叉树实现 (主要使用)
├── quad_tree.test.js        # 原始测试版本 (已弃用)
├── verify_fix.html          # 修复验证页面
├── debug_test.html          # 调试测试页面
├── simple_test.html         # 简单验证测试
├── minimal_test.html        # 最小化测试
└── README.md               # 本文件
```

## 🚀 快速开始

### 方法1: 直接在浏览器中打开
1. 在浏览器中打开 `tests/index.html`
2. 选择要运行的测试类型
3. 点击对应的测试按钮

### 方法2: 使用本地服务器
```bash
# 在项目根目录运行
pnpm run preview
# 然后访问 http://localhost:3001/tests/
```

## 🧪 测试类型

### 1. 功能测试 (`quad_tree.test.html`)
**目的**: 验证四叉树的基础功能是否正确

**测试内容**:
- ✅ 四叉树创建和初始化
- ✅ 节点插入操作
- ✅ 范围查询功能
- ✅ 节点删除操作
- ✅ 节点位置更新
- ✅ 边界条件处理
- ✅ 错误处理机制

**运行方式**:
- 点击"运行所有测试"执行完整测试
- 或选择特定的测试类型单独运行

### 2. 可视化测试 (`quad_tree_visual.html`)
**目的**: 通过可视化界面直观了解四叉树的工作原理

**功能特性**:
- 🎨 实时四叉树结构可视化
- 🖱️ 点击添加节点
- 📦 拖拽创建查询区域
- ⚙️ 动态调整参数
- 🎯 查询结果高亮显示

**操作说明**:
- 点击画布添加新节点
- 拖拽鼠标创建查询区域
- 使用控制面板调整参数
- 观察四叉树的分割和查询过程

### 3. 性能基准测试 (`quad_tree_benchmark.html`)
**目的**: 评估四叉树在大规模数据下的性能表现

**测试项目**:
- ⚡ 插入性能测试 (1K-100K节点)
- 🔍 查询性能测试 (批量查询)
- 📈 可扩展性测试 (不同数据规模)
- ⚖️ 配置参数对比测试

**性能指标**:
- 插入速度 (节点/秒)
- 查询速度 (查询/秒)
- 内存使用效率
- 树结构深度和平衡性

### 4. 视口查询测试
**目的**: 验证矩阵变换后的视口查询功能

**测试场景**:
- 🔄 矩阵变换支持
- 📐 视口边界计算
- 🗂️ 空间索引构建
- 🎥 动态视口更新

### 5. 动态操作测试
**目的**: 测试四叉树的动态更新能力

**测试内容**:
- ➕ 动态添加节点
- ➖ 动态删除节点
- 🔄 节点位置更新
- 🌳 树结构自动调整

## 📊 测试参数配置

### 基础参数
- **节点数量**: 控制测试数据的规模 (1K-100K)
- **最大节点数/象限**: 四叉树分割阈值 (5-50)
- **最大深度**: 限制树的最大深度 (3-10)
- **世界大小**: 测试空间的尺寸
- **查询区域大小**: 查询范围的大小

### 性能参数
- **查询次数**: 性能测试中的查询数量
- **批处理大小**: 批量操作的大小
- **测试轮次**: 重复测试的次数

## 🎯 测试重点

### 1. 按范围建立四叉树
- ✅ 验证四叉树能够正确处理指定范围内的节点
- ✅ 测试边界条件和极端情况
- ✅ 确保树结构的正确性

### 2. 矩阵变换后的空间索引
- ✅ 验证节点经过矩阵变换后的世界坐标计算
- ✅ 测试变换后的包围盒计算
- ✅ 确保空间索引的准确性

### 3. 动态增加节点
- ✅ 测试运行时添加新节点
- ✅ 验证树结构的自动调整
- ✅ 确保数据一致性

### 4. 动态删除节点
- ✅ 测试节点删除操作
- ✅ 验证删除后的树结构完整性
- ✅ 测试删除不存在节点的处理

### 5. 按视口搜索
- ✅ 测试视口范围内的节点查询
- ✅ 验证查询结果的准确性
- ✅ 测试不同视口大小和位置的查询

## 📈 性能指标

### 插入性能
- **目标**: > 10,000 节点/秒
- **测试**: 大批量节点插入
- **指标**: 平均插入时间、总耗时

### 查询性能
- **目标**: > 1,000 查询/秒
- **测试**: 随机范围查询
- **指标**: 平均查询时间、查询准确率

### 内存效率
- **目标**: 合理的内存使用
- **测试**: 大规模数据下的内存占用
- **指标**: 内存使用量、树结构效率

### 可扩展性
- **目标**: 线性或近线性扩展
- **测试**: 不同数据规模下的性能
- **指标**: 性能随数据量的变化趋势

## 🔧 故障排除

### 已修复的问题

1. **测试3查询节点问题** ✅ **已完全修复**
   - **问题**: 查询范围 `{ minX: -50, minY: -50, maxX: 150, maxY: 150 }` 查询到0个节点
   - **根本原因**: `intersects` 方法的实现在复杂表达式中存在计算问题
   - **修复方案**: 重写 `intersects` 方法，使用分步计算避免逻辑错误
   - **验证结果**: ✅ 现在能正确查询到期望的2个节点（test1和test3）

2. **动态测试7更新结果问题** ✅ **已完全修复**
   - **问题**: 节点更新后在新位置找不到
   - **根本原因**: 查询功能失效导致更新验证失败
   - **修复方案**: 修复查询功能后，更新功能自动恢复正常
   - **验证结果**: ✅ 现在能在新位置正确找到更新后的节点

3. **四叉树实现全面修复** ✅ **已完成**
   - **Node.js版本**: `tests/quad_tree.node.test.js` - 参考实现，100%通过测试
   - **HTML版本**: `tests/quad_tree_fixed.js` - 已修复，与Node.js版本一致
   - **TypeScript版本**: `src/quad_tree.ts` - 已更新，应用相同修复
   - **测试页面**: `tests/quad_tree.test.html` - 使用修复后的内联实现

4. **验证体系完善** ✅ **已完成**
   - **Node.js测试**: 真实环境验证，100%成功率
   - **HTML验证**: 模拟HTML环境测试，功能正常
   - **跨版本验证**: 确保所有版本逻辑一致
   - **最终验证**: `final_verification_all.js` 全面检查通过

### 常见问题

1. **测试页面无法加载**
   - 确保所有测试文件都在正确位置
   - 检查浏览器控制台的错误信息
   - 尝试使用本地服务器运行

2. **性能测试运行缓慢**
   - 减少测试数据规模
   - 关闭浏览器的其他标签页
   - 使用性能更好的设备

3. **可视化显示异常**
   - 检查浏览器是否支持Canvas 2D
   - 确保JavaScript已启用
   - 尝试刷新页面

### 调试工具

- `simple_test.html`: 最基础的功能验证
- `debug_test.html`: 详细的调试信息输出
- 浏览器控制台: 查看详细的执行日志

### 浏览器兼容性
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

## 📝 测试报告

测试完成后，可以通过以下方式查看结果：
1. 浏览器控制台输出
2. 页面上的统计信息
3. 可视化图表和指标

## 🤝 贡献

如果发现测试中的问题或有改进建议，请：
1. 记录详细的错误信息
2. 提供复现步骤
3. 建议改进方案

## 📄 许可证

本测试套件遵循项目的整体许可证。

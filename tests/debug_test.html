<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四叉树调试测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a9e;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>四叉树调试测试</h1>
        
        <button onclick="testBasicInsertion()">测试基础插入</button>
        <button onclick="testBasicQuery()">测试基础查询</button>
        <button onclick="testUpdate()">测试更新操作</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="results"></div>
    </div>

    <script src="quad_tree.test.js"></script>
    <script>
        window.DEBUG_QUADTREE = true;
        
        function addResult(title, success, message, details = '') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${success ? 'success' : 'error'}`;
            
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <p>${message}</p>
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        function testBasicInsertion() {
            console.log('=== 测试基础插入 ===');
            
            // 创建四叉树
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);
            
            console.log('四叉树边界:', bounds);
            
            // 测试节点
            const testNodes = [
                { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
            ];
            
            console.log('要插入的节点:', testNodes);
            
            // 插入节点
            let insertResults = [];
            testNodes.forEach(node => {
                console.log(`\n--- 插入节点 ${node.id} ---`);
                const result = quadTree.insert(node);
                insertResults.push({ id: node.id, success: result });
                console.log(`插入结果: ${result}`);
            });
            
            const stats = quadTree.getStats();
            console.log('插入后统计:', stats);
            
            addResult(
                '基础插入测试',
                insertResults.every(r => r.success),
                `插入了 ${insertResults.filter(r => r.success).length}/${testNodes.length} 个节点`,
                `统计信息: ${JSON.stringify(stats, null, 2)}`
            );
        }
        
        function testBasicQuery() {
            console.log('\n=== 测试基础查询 ===');

            // 创建四叉树并插入节点
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);

            const testNodes = [
                { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
            ];

            console.log('插入节点前的树状态:', quadTree.getStats());

            testNodes.forEach(node => {
                console.log(`插入节点 ${node.id}:`, node.bounds);
                const result = quadTree.insert(node);
                console.log(`插入结果: ${result}`);
            });

            console.log('插入节点后的树状态:', quadTree.getStats());

            // 先查询整个树看看有什么
            const allNodes = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            console.log('树中所有节点:', allNodes);

            // 查询测试
            const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            console.log('查询范围:', queryRange);

            console.log('\n--- 开始查询 ---');
            const found = quadTree.query(queryRange);
            console.log('查询结果:', found);

            // 手动验证相交逻辑
            console.log('\n--- 手动验证相交逻辑 ---');
            testNodes.forEach(node => {
                const intersects = !(queryRange.maxX < node.bounds.minX || queryRange.minX > node.bounds.maxX ||
                                    queryRange.maxY < node.bounds.minY || queryRange.minY > node.bounds.minY);
                console.log(`节点 ${node.id}:`, {
                    bounds: node.bounds,
                    intersects: intersects,
                    details: {
                        'queryRange.maxX < node.bounds.minX': queryRange.maxX < node.bounds.minX,
                        'queryRange.minX > node.bounds.maxX': queryRange.minX > node.bounds.maxX,
                        'queryRange.maxY < node.bounds.minY': queryRange.maxY < node.bounds.minY,
                        'queryRange.minY > node.bounds.maxY': queryRange.minY > node.bounds.maxY
                    }
                });
            });

            addResult(
                '基础查询测试',
                found.length > 0,
                `查询到 ${found.length} 个节点`,
                `查询范围: ${JSON.stringify(queryRange)}\n找到节点: ${found.map(n => n.id).join(', ')}\n所有节点: ${allNodes.map(n => n.id).join(', ')}`
            );
        }
        
        function testUpdate() {
            console.log('\n=== 测试更新操作 ===');
            
            // 创建四叉树并插入节点
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);
            
            const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
            console.log('插入初始节点:', initialNode);
            quadTree.insert(initialNode);
            
            // 验证初始插入
            const initialQuery = quadTree.query({ minX: -50, minY: -50, maxX: 150, maxY: 150 });
            console.log('初始查询结果:', initialQuery);
            
            // 更新节点位置
            const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
            console.log('\n--- 更新节点位置 ---');
            console.log('新边界:', newBounds);
            
            const updateResult = quadTree.update('dynamic1', newBounds);
            console.log('更新结果:', updateResult);
            
            // 验证更新后的位置
            console.log('\n--- 验证更新后位置 ---');
            const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
            console.log('新查询范围:', newQueryRange);
            
            const updatedQuery = quadTree.query(newQueryRange);
            console.log('更新后查询结果:', updatedQuery);
            
            // 查询整个树
            const allNodes = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            console.log('所有节点:', allNodes);
            
            const foundUpdated = updatedQuery.find(n => n.id === 'dynamic1');
            
            addResult(
                '更新操作测试',
                foundUpdated !== undefined,
                foundUpdated ? '✓ 在新位置找到更新的节点' : '✗ 未在新位置找到更新的节点',
                `更新结果: ${updateResult}\n新查询范围: ${JSON.stringify(newQueryRange)}\n找到节点: ${updatedQuery.map(n => n.id).join(', ')}\n所有节点: ${allNodes.map(n => n.id).join(', ')}`
            );
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接测试四叉树</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>直接测试四叉树</h1>
    <button onclick="runDirectTest()">运行直接测试</button>
    <div id="results"></div>

    <script src="quad_tree_fixed.js"></script>
    <script>
        window.DEBUG_QUADTREE = true;
        
        function addResult(title, success, message, details = '') {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${title}:</strong> ${success ? '✓' : '✗'} ${message}
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            document.getElementById('results').appendChild(div);
        }

        function runDirectTest() {
            document.getElementById('results').innerHTML = '';
            console.clear();
            
            console.log('=== 开始直接测试 ===');
            
            // 重现原始问题
            console.log('\n--- 重现测试3问题 ---');
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);
            
            console.log('四叉树创建完成，边界:', bounds);
            
            const testNodes = [
                { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
            ];

            console.log('要插入的节点:', testNodes);

            // 插入节点
            console.log('\n=== 开始插入节点 ===');
            let insertResults = [];
            testNodes.forEach(node => {
                console.log(`\n--- 插入节点 ${node.id} ---`);
                const result = quadTree.insert(node);
                insertResults.push({ id: node.id, success: result });
                console.log(`插入结果: ${result}`);
            });

            addResult('插入节点', insertResults.every(r => r.success), 
                     `插入结果: ${insertResults.map(r => `${r.id}: ${r.success}`).join(', ')}`);

            // 查询所有节点验证插入
            console.log('\n=== 查询所有节点 ===');
            const allNodes = quadTree.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            console.log('查询到的所有节点:', allNodes);

            addResult('查询所有节点', allNodes.length === testNodes.length, 
                     `查询到 ${allNodes.length} 个节点，期望 ${testNodes.length} 个`,
                     `节点: ${allNodes.map(n => n.id).join(', ')}`);

            // 重现问题查询
            console.log('\n=== 重现问题查询 ===');
            const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            console.log('查询范围:', queryRange);
            
            const found = quadTree.query(queryRange);
            console.log('查询结果:', found);

            // 手动验证相交逻辑
            console.log('\n=== 手动验证相交逻辑 ===');
            let expectedCount = 0;
            testNodes.forEach(node => {
                const intersects = !(queryRange.maxX < node.bounds.minX || queryRange.minX > node.bounds.maxX ||
                                    queryRange.maxY < node.bounds.minY || queryRange.minY > node.bounds.minY);
                console.log(`节点 ${node.id}:`, {
                    bounds: node.bounds,
                    intersects: intersects,
                    details: {
                        'queryRange.maxX < node.bounds.minX': queryRange.maxX < node.bounds.minX,
                        'queryRange.minX > node.bounds.maxX': queryRange.minX > node.bounds.maxX,
                        'queryRange.maxY < node.bounds.minY': queryRange.maxY < node.bounds.minY,
                        'queryRange.minY > node.bounds.maxY': queryRange.minY > node.bounds.maxY
                    }
                });
                if (intersects) expectedCount++;
            });

            addResult('问题查询', found.length === expectedCount, 
                     `查询到 ${found.length} 个节点，期望 ${expectedCount} 个`,
                     `查询范围: ${JSON.stringify(queryRange)}\n找到: ${found.map(n => n.id).join(', ')}`);

            // 重现更新问题
            console.log('\n=== 重现更新问题 ===');
            const quadTree2 = createQuadTree(bounds, 5, 4);
            
            const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
            console.log('插入初始节点:', initialNode);
            quadTree2.insert(initialNode);

            // 更新节点位置
            const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
            console.log('更新到新边界:', newBounds);
            const updateResult = quadTree2.update('dynamic1', newBounds);
            console.log('更新结果:', updateResult);

            addResult('更新节点', updateResult, `更新${updateResult ? '成功' : '失败'}`);

            // 验证更新后位置
            console.log('\n=== 验证更新后位置 ===');
            const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
            console.log('新查询范围:', newQueryRange);
            
            const foundAfterUpdate = quadTree2.query(newQueryRange);
            console.log('更新后查询结果:', foundAfterUpdate);
            
            const foundUpdated = foundAfterUpdate.find(n => n.id === 'dynamic1');

            addResult('验证更新', foundUpdated !== undefined, 
                     `在新位置${foundUpdated ? '找到' : '未找到'}节点`,
                     `查询范围: ${JSON.stringify(newQueryRange)}\n找到: ${foundAfterUpdate.map(n => n.id).join(', ')}`);

            // 查询整个树看看节点在哪里
            const allNodesAfterUpdate = quadTree2.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            console.log('更新后所有节点:', allNodesAfterUpdate);

            addResult('更新后所有节点', true, 
                     `树中共有 ${allNodesAfterUpdate.length} 个节点`,
                     `节点: ${allNodesAfterUpdate.map(n => `${n.id}: ${JSON.stringify(n.bounds)}`).join('\n')}`);

            console.log('\n=== 测试完成 ===');
        }
    </script>
</body>
</html>

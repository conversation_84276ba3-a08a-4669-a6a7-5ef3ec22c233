<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单四叉树测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>简单四叉树测试</h1>
    <div id="results"></div>

    <script src="quad_tree.test.js"></script>
    <script>
        function test(name, condition, message) {
            const div = document.createElement('div');
            div.className = `result ${condition ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${name}:</strong> ${condition ? '✓' : '✗'} ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(`${name}: ${condition ? 'PASS' : 'FAIL'} - ${message}`);
        }

        // 测试1: 创建四叉树
        const bounds = { minX: 0, minY: 0, maxX: 1000, maxY: 1000 };
        const quadTree = createQuadTree(bounds, 3, 3);
        test('创建四叉树', quadTree !== null, '四叉树创建成功');

        // 测试2: 插入单个节点
        const node1 = { id: 'node1', bounds: { minX: 100, minY: 100, maxX: 200, maxY: 200 } };
        const insertResult = quadTree.insert(node1);
        test('插入节点', insertResult === true, `节点插入${insertResult ? '成功' : '失败'}`);

        // 测试3: 查询包含该节点的范围
        const queryRange1 = { minX: 50, minY: 50, maxX: 250, maxY: 250 };
        const found1 = quadTree.query(queryRange1);
        test('查询节点', found1.length === 1 && found1[0].id === 'node1', 
             `查询到 ${found1.length} 个节点: ${found1.map(n => n.id).join(', ')}`);

        // 测试4: 查询不相交的范围
        const queryRange2 = { minX: 300, minY: 300, maxX: 400, maxY: 400 };
        const found2 = quadTree.query(queryRange2);
        test('查询空范围', found2.length === 0, `查询到 ${found2.length} 个节点`);

        // 测试5: 插入多个节点
        const node2 = { id: 'node2', bounds: { minX: 300, minY: 300, maxX: 400, maxY: 400 } };
        const node3 = { id: 'node3', bounds: { minX: 500, minY: 500, maxX: 600, maxY: 600 } };
        quadTree.insert(node2);
        quadTree.insert(node3);

        const allNodes = quadTree.query({ minX: 0, minY: 0, maxX: 1000, maxY: 1000 });
        test('插入多个节点', allNodes.length === 3, 
             `总共 ${allNodes.length} 个节点: ${allNodes.map(n => n.id).join(', ')}`);

        // 测试6: 删除节点
        const removeResult = quadTree.remove('node2');
        const afterRemove = quadTree.query({ minX: 0, minY: 0, maxX: 1000, maxY: 1000 });
        test('删除节点', removeResult && afterRemove.length === 2, 
             `删除${removeResult ? '成功' : '失败'}，剩余 ${afterRemove.length} 个节点: ${afterRemove.map(n => n.id).join(', ')}`);

        // 测试7: 更新节点位置
        const updateResult = quadTree.update('node1', { minX: 700, minY: 700, maxX: 800, maxY: 800 });
        const newQuery = quadTree.query({ minX: 650, minY: 650, maxX: 850, maxY: 850 });
        const foundUpdated = newQuery.find(n => n.id === 'node1');
        test('更新节点位置', updateResult && foundUpdated !== undefined, 
             `更新${updateResult ? '成功' : '失败'}，在新位置${foundUpdated ? '找到' : '未找到'}节点`);

        // 测试8: 验证更新后的查询
        const oldQuery = quadTree.query({ minX: 50, minY: 50, maxX: 250, maxY: 250 });
        const foundInOldPos = oldQuery.find(n => n.id === 'node1');
        test('验证旧位置', foundInOldPos === undefined, 
             `在旧位置${foundInOldPos ? '仍然找到' : '未找到'}节点`);

        // 输出最终统计
        const finalStats = quadTree.getStats();
        console.log('最终统计:', finalStats);
        
        const statsDiv = document.createElement('div');
        statsDiv.className = 'result';
        statsDiv.style.backgroundColor = '#e9ecef';
        statsDiv.innerHTML = `<strong>最终统计:</strong> 总节点数: ${finalStats.totalNodes}, 深度: ${finalStats.depth}, 叶子节点: ${finalStats.leafNodes}`;
        document.getElementById('results').appendChild(statsDiv);
    </script>
</body>
</html>

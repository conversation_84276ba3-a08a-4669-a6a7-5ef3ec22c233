<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证修复</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>验证四叉树修复</h1>
    <button onclick="runTests()">运行验证测试</button>
    <div id="results"></div>

    <script src="quad_tree_fixed.js"></script>
    <script>
        function addResult(title, success, message, details = '') {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `
                <strong>${title}:</strong> ${success ? '✓' : '✗'} ${message}
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            document.getElementById('results').appendChild(div);
        }

        function addInfo(message) {
            const div = document.createElement('div');
            div.className = 'result info';
            div.innerHTML = `<strong>信息:</strong> ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runTests() {
            document.getElementById('results').innerHTML = '';
            
            // 测试1: 重现原始问题 - 查询节点
            addInfo('测试1: 重现原始查询问题');
            
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = createQuadTree(bounds, 5, 4);
            
            const testNodes = [
                { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
                { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
                { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
            ];

            // 插入节点
            let insertSuccess = true;
            testNodes.forEach(node => {
                if (!quadTree.insert(node)) {
                    insertSuccess = false;
                }
            });

            addResult('插入节点', insertSuccess, `插入了 ${testNodes.length} 个节点`);

            // 查询节点
            const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            const found = quadTree.query(queryRange);
            
            // 手动验证相交逻辑
            let expectedCount = 0;
            testNodes.forEach(node => {
                const intersects = !(queryRange.maxX < node.bounds.minX || queryRange.minX > node.bounds.maxX ||
                                    queryRange.maxY < node.bounds.minY || queryRange.minY > node.bounds.minY);
                if (intersects) expectedCount++;
            });

            addResult('查询节点', found.length === expectedCount, 
                     `查询到 ${found.length} 个节点，期望 ${expectedCount} 个`,
                     `查询范围: ${JSON.stringify(queryRange)}, 找到: ${found.map(n => n.id).join(', ')}`);

            // 测试2: 重现原始问题 - 更新节点
            addInfo('测试2: 重现原始更新问题');
            
            const quadTree2 = createQuadTree(bounds, 5, 4);
            
            // 插入初始节点
            const initialNode = { id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
            quadTree2.insert(initialNode);

            // 更新节点位置
            const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
            const updateResult = quadTree2.update('dynamic1', newBounds);

            addResult('更新节点', updateResult, `更新${updateResult ? '成功' : '失败'}`);

            // 验证更新后的位置
            const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
            const foundAfterUpdate = quadTree2.query(newQueryRange);
            const foundUpdated = foundAfterUpdate.find(n => n.id === 'dynamic1');

            addResult('验证更新结果', foundUpdated !== undefined, 
                     `在新位置${foundUpdated ? '找到' : '未找到'}节点`,
                     `查询范围: ${JSON.stringify(newQueryRange)}, 找到: ${foundAfterUpdate.map(n => n.id).join(', ')}`);

            // 验证旧位置没有节点
            const oldQueryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            const foundInOldPos = quadTree2.query(oldQueryRange);
            const stillInOldPos = foundInOldPos.find(n => n.id === 'dynamic1');

            addResult('验证旧位置', stillInOldPos === undefined, 
                     `在旧位置${stillInOldPos ? '仍然找到' : '未找到'}节点`);

            // 测试3: 复杂场景测试
            addInfo('测试3: 复杂场景测试');
            
            const quadTree3 = createQuadTree(bounds, 3, 3);
            
            // 插入多个节点触发分割
            const manyNodes = [];
            for (let i = 0; i < 10; i++) {
                const x = (i % 3) * 200;
                const y = Math.floor(i / 3) * 200;
                manyNodes.push({
                    id: `node${i}`,
                    bounds: { minX: x, minY: y, maxX: x + 50, maxY: y + 50 }
                });
            }

            let allInserted = true;
            manyNodes.forEach(node => {
                if (!quadTree3.insert(node)) {
                    allInserted = false;
                }
            });

            addResult('批量插入', allInserted, `插入了 ${manyNodes.length} 个节点`);

            // 查询所有节点
            const allFound = quadTree3.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            addResult('查询所有节点', allFound.length === manyNodes.length, 
                     `查询到 ${allFound.length} 个节点，期望 ${manyNodes.length} 个`);

            // 删除一些节点
            const toDelete = ['node1', 'node3', 'node5'];
            let deleteSuccess = true;
            toDelete.forEach(id => {
                if (!quadTree3.remove(id)) {
                    deleteSuccess = false;
                }
            });

            addResult('批量删除', deleteSuccess, `删除了 ${toDelete.length} 个节点`);

            // 验证删除结果
            const afterDelete = quadTree3.query({ minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 });
            const expectedAfterDelete = manyNodes.length - toDelete.length;
            addResult('验证删除结果', afterDelete.length === expectedAfterDelete, 
                     `删除后剩余 ${afterDelete.length} 个节点，期望 ${expectedAfterDelete} 个`);

            // 最终统计
            const stats = quadTree3.getStats();
            addInfo(`最终统计: 总节点数 ${stats.totalNodes}, 深度 ${stats.depth}, 叶子节点 ${stats.leafNodes}`);
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四叉树可视化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .controls {
            width: 300px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            height: fit-content;
        }
        .canvas-container {
            flex: 1;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
        }
        canvas {
            border: 1px solid #ddd;
            cursor: crosshair;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px 0;
            width: 100%;
        }
        button:hover {
            background-color: #005a9e;
        }
        .info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007acc;
        }
        input[type="range"] {
            width: 100%;
            margin: 10px 0;
        }
        label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>四叉树可视化测试</h1>
    
    <div class="container">
        <div class="controls">
            <h3>控制面板</h3>
            
            <button onclick="generateRandomNodes()">生成随机节点</button>
            <button onclick="clearAll()">清空所有</button>
            <button onclick="testViewportQuery()">测试视口查询</button>
            <button onclick="testDynamicOperations()">测试动态操作</button>
            
            <label for="nodeCount">节点数量:</label>
            <input type="range" id="nodeCount" min="10" max="1000" value="100" oninput="updateNodeCount(this.value)">
            <span id="nodeCountValue">100</span>
            
            <label for="maxNodes">最大节点数/象限:</label>
            <input type="range" id="maxNodes" min="5" max="50" value="10" oninput="updateMaxNodes(this.value)">
            <span id="maxNodesValue">10</span>
            
            <label for="maxDepth">最大深度:</label>
            <input type="range" id="maxDepth" min="3" max="8" value="5" oninput="updateMaxDepth(this.value)">
            <span id="maxDepthValue">5</span>
            
            <div class="info">
                <strong>操作说明:</strong><br>
                • 点击画布添加节点<br>
                • 拖拽创建查询区域<br>
                • 红色边框显示四叉树分割<br>
                • 绿色高亮显示查询结果
            </div>
            
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value" id="totalNodes">0</div>
                    <div>总节点数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="treeDepth">0</div>
                    <div>树深度</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="leafNodes">0</div>
                    <div>叶子节点</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="queryTime">0ms</div>
                    <div>查询耗时</div>
                </div>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
    </div>

    <script src="quad_tree_fixed.js"></script>
    <script>
        // 全局变量
        let canvas, ctx;
        let quadTree;
        let nodes = [];
        let queryResult = [];
        let isDragging = false;
        let dragStart = null;
        let dragEnd = null;
        
        // 配置参数
        let config = {
            nodeCount: 100,
            maxNodes: 10,
            maxDepth: 5
        };

        // 初始化
        function init() {
            canvas = document.getElementById('canvas');
            ctx = canvas.getContext('2d');
            
            // 创建四叉树
            const bounds = { minX: 0, minY: 0, maxX: canvas.width, maxY: canvas.height };
            quadTree = createQuadTree(bounds, config.maxNodes, config.maxDepth);
            
            // 绑定事件
            canvas.addEventListener('click', onCanvasClick);
            canvas.addEventListener('mousedown', onMouseDown);
            canvas.addEventListener('mousemove', onMouseMove);
            canvas.addEventListener('mouseup', onMouseUp);
            
            // 生成初始节点
            generateRandomNodes();
        }

        // 更新配置
        function updateNodeCount(value) {
            config.nodeCount = parseInt(value);
            document.getElementById('nodeCountValue').textContent = value;
        }

        function updateMaxNodes(value) {
            config.maxNodes = parseInt(value);
            document.getElementById('maxNodesValue').textContent = value;
            rebuildQuadTree();
        }

        function updateMaxDepth(value) {
            config.maxDepth = parseInt(value);
            document.getElementById('maxDepthValue').textContent = value;
            rebuildQuadTree();
        }

        // 重建四叉树
        function rebuildQuadTree() {
            const bounds = { minX: 0, minY: 0, maxX: canvas.width, maxY: canvas.height };
            quadTree = createQuadTree(bounds, config.maxNodes, config.maxDepth);
            
            // 重新插入所有节点
            nodes.forEach(node => {
                quadTree.insert(node);
            });
            
            updateStats();
            draw();
        }

        // 生成随机节点
        function generateRandomNodes() {
            clearAll();
            
            for (let i = 0; i < config.nodeCount; i++) {
                const x = Math.random() * (canvas.width - 40) + 20;
                const y = Math.random() * (canvas.height - 40) + 20;
                const size = Math.random() * 20 + 10;
                
                const node = {
                    id: `node_${i}`,
                    bounds: {
                        minX: x - size/2,
                        minY: y - size/2,
                        maxX: x + size/2,
                        maxY: y + size/2
                    },
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                };
                
                nodes.push(node);
                quadTree.insert(node);
            }
            
            updateStats();
            draw();
        }

        // 清空所有
        function clearAll() {
            nodes = [];
            queryResult = [];
            quadTree.clear();
            updateStats();
            draw();
        }

        // 鼠标事件处理
        function onCanvasClick(e) {
            if (isDragging) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const size = 20;
            const node = {
                id: `manual_${Date.now()}`,
                bounds: {
                    minX: x - size/2,
                    minY: y - size/2,
                    maxX: x + size/2,
                    maxY: y + size/2
                },
                color: '#ff6b6b'
            };
            
            nodes.push(node);
            quadTree.insert(node);
            updateStats();
            draw();
        }

        function onMouseDown(e) {
            const rect = canvas.getBoundingClientRect();
            dragStart = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
            isDragging = true;
        }

        function onMouseMove(e) {
            if (!isDragging) return;
            
            const rect = canvas.getBoundingClientRect();
            dragEnd = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
            
            draw();
        }

        function onMouseUp(e) {
            if (!isDragging) return;
            
            isDragging = false;
            
            if (dragStart && dragEnd) {
                const queryBounds = {
                    minX: Math.min(dragStart.x, dragEnd.x),
                    minY: Math.min(dragStart.y, dragEnd.y),
                    maxX: Math.max(dragStart.x, dragEnd.x),
                    maxY: Math.max(dragStart.y, dragEnd.y)
                };
                
                const startTime = performance.now();
                queryResult = quadTree.query(queryBounds);
                const queryTime = performance.now() - startTime;
                
                document.getElementById('queryTime').textContent = `${queryTime.toFixed(2)}ms`;
                
                draw();
            }
            
            dragStart = null;
            dragEnd = null;
        }

        // 测试视口查询
        function testViewportQuery() {
            const viewportBounds = {
                minX: canvas.width * 0.25,
                minY: canvas.height * 0.25,
                maxX: canvas.width * 0.75,
                maxY: canvas.height * 0.75
            };
            
            const startTime = performance.now();
            queryResult = quadTree.query(viewportBounds);
            const queryTime = performance.now() - startTime;
            
            document.getElementById('queryTime').textContent = `${queryTime.toFixed(2)}ms`;
            
            // 设置拖拽区域以显示查询范围
            dragStart = { x: viewportBounds.minX, y: viewportBounds.minY };
            dragEnd = { x: viewportBounds.maxX, y: viewportBounds.maxY };
            
            draw();
        }

        // 测试动态操作
        function testDynamicOperations() {
            // 删除一些节点
            const toDelete = nodes.slice(0, Math.min(5, nodes.length));
            toDelete.forEach(node => {
                quadTree.remove(node.id);
                const index = nodes.findIndex(n => n.id === node.id);
                if (index !== -1) {
                    nodes.splice(index, 1);
                }
            });
            
            // 添加一些新节点
            for (let i = 0; i < 5; i++) {
                const x = Math.random() * (canvas.width - 40) + 20;
                const y = Math.random() * (canvas.height - 40) + 20;
                const size = Math.random() * 30 + 15;
                
                const node = {
                    id: `dynamic_${Date.now()}_${i}`,
                    bounds: {
                        minX: x - size/2,
                        minY: y - size/2,
                        maxX: x + size/2,
                        maxY: y + size/2
                    },
                    color: '#4ecdc4'
                };
                
                nodes.push(node);
                quadTree.insert(node);
            }
            
            updateStats();
            draw();
        }

        // 更新统计信息
        function updateStats() {
            const stats = quadTree.getStats();
            document.getElementById('totalNodes').textContent = stats.totalNodes;
            document.getElementById('treeDepth').textContent = stats.depth;
            document.getElementById('leafNodes').textContent = stats.leafNodes;
        }

        // 绘制函数
        function draw() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制四叉树边界
            drawQuadTreeBounds(quadTree);
            
            // 绘制节点
            nodes.forEach(node => {
                const isHighlighted = queryResult.some(q => q.id === node.id);
                drawNode(node, isHighlighted);
            });
            
            // 绘制拖拽区域
            if (dragStart && dragEnd) {
                drawQueryArea(dragStart, dragEnd);
            }
        }

        // 绘制四叉树边界
        function drawQuadTreeBounds(tree) {
            ctx.strokeStyle = '#ff4757';
            ctx.lineWidth = 1;
            ctx.setLineDash([5, 5]);
            
            const { minX, minY, maxX, maxY } = tree.bounds;
            ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);
            
            if (tree.divided) {
                tree.children.forEach(child => drawQuadTreeBounds(child));
            }
            
            ctx.setLineDash([]);
        }

        // 绘制节点
        function drawNode(node, highlighted = false) {
            const { minX, minY, maxX, maxY } = node.bounds;
            const width = maxX - minX;
            const height = maxY - minY;
            
            ctx.fillStyle = highlighted ? '#2ed573' : (node.color || '#3742fa');
            ctx.fillRect(minX, minY, width, height);
            
            if (highlighted) {
                ctx.strokeStyle = '#2ed573';
                ctx.lineWidth = 3;
                ctx.strokeRect(minX, minY, width, height);
            }
        }

        // 绘制查询区域
        function drawQueryArea(start, end) {
            const minX = Math.min(start.x, end.x);
            const minY = Math.min(start.y, end.y);
            const width = Math.abs(end.x - start.x);
            const height = Math.abs(end.y - start.y);
            
            ctx.strokeStyle = '#ffa502';
            ctx.lineWidth = 2;
            ctx.setLineDash([10, 5]);
            ctx.strokeRect(minX, minY, width, height);
            
            ctx.fillStyle = 'rgba(255, 165, 2, 0.1)';
            ctx.fillRect(minX, minY, width, height);
            
            ctx.setLineDash([]);
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>
</html>

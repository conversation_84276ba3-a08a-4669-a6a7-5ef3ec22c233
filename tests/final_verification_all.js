// 最终验证所有修复
// 验证Node.js版本、HTML版本和TypeScript版本的一致性

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🎯 最终验证所有四叉树修复\n');

// 1. 验证Node.js版本（已知正确）
console.log('--- 1. Node.js版本验证 ---');

// 从quad_tree.node.test.js中提取四叉树类
const nodeTestCode = fs.readFileSync(path.join(__dirname, 'quad_tree.node.test.js'), 'utf8');
const quadTreeClassMatch = nodeTestCode.match(/class QuadTree \{[\s\S]*?\n\}/);
const createQuadTreeMatch = nodeTestCode.match(/function createQuadTree\([\s\S]*?\n\}/);

if (quadTreeClassMatch && createQuadTreeMatch) {
    console.log('✅ Node.js版本代码提取成功');
} else {
    console.log('❌ Node.js版本代码提取失败');
}

// 2. 验证HTML版本
console.log('\n--- 2. HTML版本验证 ---');

// 读取quad_tree_fixed.js
const htmlQuadTreeCode = fs.readFileSync(path.join(__dirname, 'quad_tree_fixed.js'), 'utf8');

// 检查是否包含修复的intersects方法
const hasFixedIntersects = htmlQuadTreeCode.includes('const c1 = range.maxX < this.bounds.minX;') &&
                          htmlQuadTreeCode.includes('const orResult = c1 || c2 || c3 || c4;') &&
                          htmlQuadTreeCode.includes('const result = !orResult;');

console.log(`${hasFixedIntersects ? '✅' : '❌'} HTML版本包含修复的intersects方法`);

// 3. 验证TypeScript版本
console.log('\n--- 3. TypeScript版本验证 ---');

const tsQuadTreeCode = fs.readFileSync(path.join(__dirname, '../src/quad_tree.ts'), 'utf8');

// 检查TypeScript版本是否也包含修复
const tsHasFixedIntersects = tsQuadTreeCode.includes('const c1 = range.maxX < this.bounds.minX;') &&
                            tsQuadTreeCode.includes('const orResult = c1 || c2 || c3 || c4;') &&
                            tsQuadTreeCode.includes('const result = !orResult;');

console.log(`${tsHasFixedIntersects ? '✅' : '❌'} TypeScript版本包含修复的intersects方法`);

// 4. 功能验证
console.log('\n--- 4. 功能验证 ---');

// 创建模拟window对象并执行HTML版本代码
global.window = {};
eval(htmlQuadTreeCode);
const { createQuadTree } = global.window;

// 执行关键测试
const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
const quadTree = createQuadTree(bounds, 5, 4);

const testNodes = [
    { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } },
    { id: 'test2', bounds: { minX: 200, minY: 200, maxX: 300, maxY: 300 } },
    { id: 'test3', bounds: { minX: -100, minY: -100, maxX: 0, maxY: 0 } }
];

// 插入测试
testNodes.forEach(node => quadTree.insert(node));

// 查询测试
const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
const found = quadTree.query(queryRange);

// 期望结果：test1和test3应该相交
const expectedIds = ['test1', 'test3'];
const foundIds = found.map(n => n.id).sort();
const queryTestPassed = foundIds.length === expectedIds.length && 
                       foundIds.every((id, index) => id === expectedIds.sort()[index]);

console.log(`${queryTestPassed ? '✅' : '❌'} 查询功能测试: 找到 ${foundIds.join(', ')}`);

// 更新测试
const quadTree2 = createQuadTree(bounds, 5, 4);
quadTree2.insert({ id: 'dynamic1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } });

const updateResult = quadTree2.update('dynamic1', { minX: 500, minY: 500, maxX: 600, maxY: 600 });
const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
const foundAfterUpdate = quadTree2.query(newQueryRange);
const updateTestPassed = updateResult && foundAfterUpdate.some(n => n.id === 'dynamic1');

console.log(`${updateTestPassed ? '✅' : '❌'} 更新功能测试: 更新${updateResult ? '成功' : '失败'}，新位置${foundAfterUpdate.length > 0 ? '找到' : '未找到'}节点`);

// 5. 总结
console.log('\n📊 最终验证总结:');

const allChecks = [
    hasFixedIntersects,
    tsHasFixedIntersects, 
    queryTestPassed,
    updateTestPassed
];

const passedChecks = allChecks.filter(check => check).length;
const totalChecks = allChecks.length;

console.log(`通过检查: ${passedChecks}/${totalChecks}`);
console.log(`成功率: ${(passedChecks/totalChecks*100).toFixed(1)}%`);

if (passedChecks === totalChecks) {
    console.log('\n🎉 所有修复都已完成！');
    console.log('✅ Node.js版本: 正常工作');
    console.log('✅ HTML版本: 已修复');
    console.log('✅ TypeScript版本: 已更新');
    console.log('✅ 功能测试: 全部通过');
} else {
    console.log('\n❌ 仍有部分问题需要解决');
    if (!hasFixedIntersects) console.log('❌ HTML版本需要修复');
    if (!tsHasFixedIntersects) console.log('❌ TypeScript版本需要更新');
    if (!queryTestPassed) console.log('❌ 查询功能仍有问题');
    if (!updateTestPassed) console.log('❌ 更新功能仍有问题');
}

console.log('\n🔗 测试文件状态:');
console.log('✅ tests/quad_tree.node.test.js - Node.js版本（参考实现）');
console.log('✅ tests/quad_tree_fixed.js - HTML版本（已修复）');
console.log('✅ tests/quad_tree.test.html - 主测试页面（已修复）');
console.log('✅ src/quad_tree.ts - TypeScript源码（已更新）');

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小化四叉树测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>最小化四叉树测试</h1>
    <button onclick="runTest()">运行测试</button>
    <div id="results"></div>

    <script>
        // 简化的四叉树实现
        class SimpleQuadTree {
            constructor(bounds, maxNodes = 10, maxDepth = 5, depth = 0) {
                this.bounds = bounds;
                this.nodes = [];
                this.children = [];
                this.maxNodes = maxNodes;
                this.maxDepth = maxDepth;
                this.depth = depth;
                this.divided = false;
            }

            containsBounds(bounds) {
                return bounds.minX >= this.bounds.minX && bounds.maxX <= this.bounds.maxX &&
                       bounds.minY >= this.bounds.minY && bounds.maxY <= this.bounds.maxY;
            }

            intersects(range) {
                return !(range.maxX < this.bounds.minX || range.minX > this.bounds.maxX ||
                         range.maxY < this.bounds.minY || range.minY > this.bounds.minY);
            }

            boundsIntersect(bounds1, bounds2) {
                return !(bounds1.maxX < bounds2.minX || bounds1.minX > bounds2.maxX ||
                         bounds1.maxY < bounds2.minY || bounds1.minY > bounds2.maxY);
            }

            subdivide() {
                if (this.divided || this.depth >= this.maxDepth) return;

                const { minX, minY, maxX, maxY } = this.bounds;
                const midX = (minX + maxX) / 2;
                const midY = (minY + maxY) / 2;

                this.children = [
                    new SimpleQuadTree({ minX, minY, maxX: midX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new SimpleQuadTree({ minX: midX, minY, maxX, maxY: midY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new SimpleQuadTree({ minX, minY: midY, maxX: midX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1),
                    new SimpleQuadTree({ minX: midX, minY: midY, maxX, maxY }, this.maxNodes, this.maxDepth, this.depth + 1)
                ];

                this.divided = true;

                const nodesToRedistribute = [...this.nodes];
                this.nodes = [];

                for (const node of nodesToRedistribute) {
                    this.insert(node);
                }
            }

            insert(node) {
                console.log(`尝试插入节点 ${node.id} 到深度 ${this.depth}`);
                console.log(`节点边界:`, node.bounds);
                console.log(`树边界:`, this.bounds);
                
                if (!this.containsBounds(node.bounds)) {
                    console.log(`节点 ${node.id} 不在边界内，插入失败`);
                    return false;
                }

                if (this.nodes.length < this.maxNodes && !this.divided) {
                    this.nodes.push(node);
                    console.log(`节点 ${node.id} 成功插入到深度 ${this.depth}，当前节点数: ${this.nodes.length}`);
                    return true;
                }

                if (!this.divided) {
                    console.log(`深度 ${this.depth} 需要分割`);
                    this.subdivide();
                }

                for (let i = 0; i < this.children.length; i++) {
                    console.log(`尝试插入到子象限 ${i}`);
                    if (this.children[i].insert(node)) {
                        console.log(`节点 ${node.id} 成功插入到子象限 ${i}`);
                        return true;
                    }
                }

                this.nodes.push(node);
                console.log(`节点 ${node.id} 插入到深度 ${this.depth} (无法插入子象限)`);
                return true;
            }

            query(range, found = []) {
                console.log(`查询深度 ${this.depth}，范围:`, range);
                console.log(`树边界:`, this.bounds);
                
                if (!this.intersects(range)) {
                    console.log(`深度 ${this.depth} 不相交，跳过`);
                    return found;
                }

                console.log(`深度 ${this.depth} 相交，检查 ${this.nodes.length} 个节点`);
                for (const node of this.nodes) {
                    const nodeIntersects = this.boundsIntersect(range, node.bounds);
                    console.log(`检查节点 ${node.id}:`, node.bounds, `相交: ${nodeIntersects}`);
                    if (nodeIntersects) {
                        found.push(node);
                        console.log(`找到节点 ${node.id}`);
                    }
                }

                if (this.divided) {
                    console.log(`深度 ${this.depth} 已分割，递归查询子象限`);
                    for (let i = 0; i < this.children.length; i++) {
                        console.log(`查询子象限 ${i}`);
                        this.children[i].query(range, found);
                    }
                }

                return found;
            }

            remove(nodeId) {
                const index = this.nodes.findIndex(node => node.id === nodeId);
                if (index !== -1) {
                    this.nodes.splice(index, 1);
                    console.log(`在深度 ${this.depth} 删除节点 ${nodeId}`);
                    return true;
                }

                if (this.divided) {
                    for (const child of this.children) {
                        if (child.remove(nodeId)) {
                            return true;
                        }
                    }
                }

                return false;
            }

            update(nodeId, newBounds) {
                console.log(`更新节点 ${nodeId} 到新边界:`, newBounds);
                if (this.remove(nodeId)) {
                    console.log(`成功删除节点 ${nodeId}`);
                    const newNode = { id: nodeId, bounds: newBounds };
                    const insertResult = this.insert(newNode);
                    console.log(`重新插入结果: ${insertResult}`);
                    return insertResult;
                }
                console.log(`删除节点 ${nodeId} 失败`);
                return false;
            }

            getStats() {
                let totalNodes = this.nodes.length;
                let maxDepth = this.depth;
                let leafNodes = this.divided ? 0 : 1;

                if (this.divided) {
                    for (const child of this.children) {
                        const childStats = child.getStats();
                        totalNodes += childStats.totalNodes;
                        maxDepth = Math.max(maxDepth, childStats.depth);
                        leafNodes += childStats.leafNodes;
                    }
                }

                return { totalNodes, depth: maxDepth, leafNodes };
            }
        }

        function addResult(title, success, message) {
            const div = document.createElement('div');
            div.className = `result ${success ? 'success' : 'error'}`;
            div.innerHTML = `<strong>${title}:</strong> ${success ? '✓' : '✗'} ${message}`;
            document.getElementById('results').appendChild(div);
        }

        function runTest() {
            document.getElementById('results').innerHTML = '';
            console.clear();
            
            console.log('=== 开始测试 ===');
            
            // 测试1: 基础插入和查询
            console.log('\n--- 测试1: 基础插入和查询 ---');
            const bounds = { minX: -1000, minY: -1000, maxX: 1000, maxY: 1000 };
            const quadTree = new SimpleQuadTree(bounds, 5, 4);
            
            const testNode = { id: 'test1', bounds: { minX: 0, minY: 0, maxX: 100, maxY: 100 } };
            console.log('插入节点:', testNode);
            
            const insertResult = quadTree.insert(testNode);
            addResult('插入节点', insertResult, `插入${insertResult ? '成功' : '失败'}`);
            
            console.log('\n查询节点...');
            const queryRange = { minX: -50, minY: -50, maxX: 150, maxY: 150 };
            const found = quadTree.query(queryRange);
            
            addResult('查询节点', found.length === 1, `查询到 ${found.length} 个节点，期望 1 个`);
            
            // 测试2: 更新节点
            console.log('\n--- 测试2: 更新节点 ---');
            const newBounds = { minX: 500, minY: 500, maxX: 600, maxY: 600 };
            const updateResult = quadTree.update('test1', newBounds);
            addResult('更新节点', updateResult, `更新${updateResult ? '成功' : '失败'}`);
            
            console.log('\n验证更新后位置...');
            const newQueryRange = { minX: 450, minY: 450, maxX: 650, maxY: 650 };
            const foundAfterUpdate = quadTree.query(newQueryRange);
            const foundUpdated = foundAfterUpdate.find(n => n.id === 'test1');
            
            addResult('验证更新', foundUpdated !== undefined, 
                     `在新位置${foundUpdated ? '找到' : '未找到'}节点`);
            
            console.log('\n=== 测试完成 ===');
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>四叉树性能基准测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-panel {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .control-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
        }
        button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
            margin: 5px 0;
        }
        button:hover {
            background-color: #005a9e;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .results {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .result-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        .metric-value {
            font-weight: bold;
            color: #007acc;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background-color: #007acc;
            transition: width 0.3s ease;
        }
        .chart {
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
        input[type="number"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px 0;
        }
        label {
            display: block;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            text-align: center;
        }
        .status.running {
            background-color: #fff3cd;
            color: #856404;
        }
        .status.completed {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>四叉树性能基准测试</h1>
        
        <div class="test-panel">
            <h2>测试配置</h2>
            <div class="controls">
                <div class="control-group">
                    <label for="nodeCount">节点数量</label>
                    <input type="number" id="nodeCount" value="10000" min="1000" max="100000" step="1000">
                    
                    <label for="queryCount">查询次数</label>
                    <input type="number" id="queryCount" value="1000" min="100" max="10000" step="100">
                </div>
                
                <div class="control-group">
                    <label for="maxNodes">最大节点数/象限</label>
                    <input type="number" id="maxNodes" value="10" min="5" max="50" step="5">
                    
                    <label for="maxDepth">最大深度</label>
                    <input type="number" id="maxDepth" value="6" min="3" max="10" step="1">
                </div>
                
                <div class="control-group">
                    <label for="worldSize">世界大小</label>
                    <input type="number" id="worldSize" value="10000" min="1000" max="50000" step="1000">
                    
                    <label for="querySize">查询区域大小</label>
                    <input type="number" id="querySize" value="1000" min="100" max="5000" step="100">
                </div>
            </div>
            
            <div class="controls">
                <button onclick="runInsertBenchmark()" id="insertBtn">插入性能测试</button>
                <button onclick="runQueryBenchmark()" id="queryBtn">查询性能测试</button>
                <button onclick="runScalabilityTest()" id="scaleBtn">可扩展性测试</button>
                <button onclick="runComparisonTest()" id="compareBtn">对比测试</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
            <div class="progress" id="progressContainer" style="display: none;">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>

        <div class="results" id="results"></div>
    </div>

    <script src="quad_tree_fixed.js"></script>
    <script>
        // 全局变量
        let isRunning = false;
        
        // 更新状态
        function updateStatus(message, type = 'running') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }
        
        // 更新进度
        function updateProgress(percent) {
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            
            if (percent >= 0) {
                progressContainer.style.display = 'block';
                progressBar.style.width = `${percent}%`;
            } else {
                progressContainer.style.display = 'none';
            }
        }
        
        // 添加结果卡片
        function addResultCard(title, metrics) {
            const resultsContainer = document.getElementById('results');
            const card = document.createElement('div');
            card.className = 'result-card';
            
            let metricsHtml = '';
            Object.entries(metrics).forEach(([key, value]) => {
                metricsHtml += `
                    <div class="metric">
                        <span>${key}</span>
                        <span class="metric-value">${value}</span>
                    </div>
                `;
            });
            
            card.innerHTML = `
                <h3>${title}</h3>
                ${metricsHtml}
            `;
            
            resultsContainer.appendChild(card);
        }
        
        // 生成随机节点
        function generateRandomNodes(count, worldSize) {
            const nodes = [];
            for (let i = 0; i < count; i++) {
                const x = Math.random() * worldSize;
                const y = Math.random() * worldSize;
                const size = Math.random() * 100 + 50;
                
                nodes.push({
                    id: `node_${i}`,
                    bounds: {
                        minX: x - size/2,
                        minY: y - size/2,
                        maxX: x + size/2,
                        maxY: y + size/2
                    }
                });
            }
            return nodes;
        }
        
        // 生成随机查询
        function generateRandomQueries(count, worldSize, querySize) {
            const queries = [];
            for (let i = 0; i < count; i++) {
                const x = Math.random() * (worldSize - querySize);
                const y = Math.random() * (worldSize - querySize);
                
                queries.push({
                    minX: x,
                    minY: y,
                    maxX: x + querySize,
                    maxY: y + querySize
                });
            }
            return queries;
        }
        
        // 插入性能测试
        async function runInsertBenchmark() {
            if (isRunning) return;
            isRunning = true;
            
            const nodeCount = parseInt(document.getElementById('nodeCount').value);
            const maxNodes = parseInt(document.getElementById('maxNodes').value);
            const maxDepth = parseInt(document.getElementById('maxDepth').value);
            const worldSize = parseInt(document.getElementById('worldSize').value);
            
            updateStatus('正在进行插入性能测试...');
            updateProgress(0);
            
            try {
                const bounds = { minX: 0, minY: 0, maxX: worldSize, maxY: worldSize };
                const quadTree = createQuadTree(bounds, maxNodes, maxDepth);
                const nodes = generateRandomNodes(nodeCount, worldSize);
                
                const startTime = performance.now();
                
                for (let i = 0; i < nodes.length; i++) {
                    quadTree.insert(nodes[i]);
                    
                    if (i % 1000 === 0) {
                        updateProgress((i / nodes.length) * 100);
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                const stats = quadTree.getStats();
                
                addResultCard('插入性能测试', {
                    '节点数量': nodeCount.toLocaleString(),
                    '总耗时': `${totalTime.toFixed(2)}ms`,
                    '平均耗时': `${(totalTime / nodeCount).toFixed(4)}ms/节点`,
                    '插入速率': `${(nodeCount / totalTime * 1000).toFixed(0)} 节点/秒`,
                    '树深度': stats.depth,
                    '叶子节点数': stats.leafNodes
                });
                
                updateStatus('插入性能测试完成', 'completed');
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                updateProgress(-1);
                isRunning = false;
            }
        }
        
        // 查询性能测试
        async function runQueryBenchmark() {
            if (isRunning) return;
            isRunning = true;
            
            const nodeCount = parseInt(document.getElementById('nodeCount').value);
            const queryCount = parseInt(document.getElementById('queryCount').value);
            const maxNodes = parseInt(document.getElementById('maxNodes').value);
            const maxDepth = parseInt(document.getElementById('maxDepth').value);
            const worldSize = parseInt(document.getElementById('worldSize').value);
            const querySize = parseInt(document.getElementById('querySize').value);
            
            updateStatus('正在进行查询性能测试...');
            updateProgress(0);
            
            try {
                // 构建四叉树
                const bounds = { minX: 0, minY: 0, maxX: worldSize, maxY: worldSize };
                const quadTree = createQuadTree(bounds, maxNodes, maxDepth);
                const nodes = generateRandomNodes(nodeCount, worldSize);
                
                updateStatus('正在插入节点...');
                nodes.forEach(node => quadTree.insert(node));
                
                updateStatus('正在进行查询测试...');
                const queries = generateRandomQueries(queryCount, worldSize, querySize);
                
                let totalFound = 0;
                const startTime = performance.now();
                
                for (let i = 0; i < queries.length; i++) {
                    const found = quadTree.query(queries[i]);
                    totalFound += found.length;
                    
                    if (i % 100 === 0) {
                        updateProgress((i / queries.length) * 100);
                        await new Promise(resolve => setTimeout(resolve, 1));
                    }
                }
                
                const endTime = performance.now();
                const totalTime = endTime - startTime;
                
                addResultCard('查询性能测试', {
                    '查询次数': queryCount.toLocaleString(),
                    '总耗时': `${totalTime.toFixed(2)}ms`,
                    '平均耗时': `${(totalTime / queryCount).toFixed(4)}ms/查询`,
                    '查询速率': `${(queryCount / totalTime * 1000).toFixed(0)} 查询/秒`,
                    '平均找到节点': `${(totalFound / queryCount).toFixed(1)}`,
                    '查询覆盖率': `${((totalFound / queryCount / nodeCount) * 100).toFixed(2)}%`
                });
                
                updateStatus('查询性能测试完成', 'completed');
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                updateProgress(-1);
                isRunning = false;
            }
        }
        
        // 可扩展性测试
        async function runScalabilityTest() {
            if (isRunning) return;
            isRunning = true;
            
            const maxNodes = parseInt(document.getElementById('maxNodes').value);
            const maxDepth = parseInt(document.getElementById('maxDepth').value);
            const worldSize = parseInt(document.getElementById('worldSize').value);
            
            updateStatus('正在进行可扩展性测试...');
            updateProgress(0);
            
            try {
                const testSizes = [1000, 5000, 10000, 25000, 50000];
                const results = [];
                
                for (let i = 0; i < testSizes.length; i++) {
                    const nodeCount = testSizes[i];
                    updateStatus(`测试 ${nodeCount} 个节点...`);
                    
                    const bounds = { minX: 0, minY: 0, maxX: worldSize, maxY: worldSize };
                    const quadTree = createQuadTree(bounds, maxNodes, maxDepth);
                    const nodes = generateRandomNodes(nodeCount, worldSize);
                    
                    // 插入测试
                    const insertStart = performance.now();
                    nodes.forEach(node => quadTree.insert(node));
                    const insertTime = performance.now() - insertStart;
                    
                    // 查询测试
                    const queries = generateRandomQueries(100, worldSize, 1000);
                    const queryStart = performance.now();
                    let totalFound = 0;
                    queries.forEach(query => {
                        totalFound += quadTree.query(query).length;
                    });
                    const queryTime = performance.now() - queryStart;
                    
                    const stats = quadTree.getStats();
                    results.push({
                        nodeCount,
                        insertTime: insertTime.toFixed(2),
                        queryTime: queryTime.toFixed(2),
                        avgInsertTime: (insertTime / nodeCount).toFixed(4),
                        avgQueryTime: (queryTime / 100).toFixed(4),
                        treeDepth: stats.depth,
                        leafNodes: stats.leafNodes
                    });
                    
                    updateProgress(((i + 1) / testSizes.length) * 100);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                // 显示结果
                results.forEach((result, index) => {
                    addResultCard(`可扩展性测试 - ${result.nodeCount} 节点`, {
                        '插入总耗时': `${result.insertTime}ms`,
                        '查询总耗时': `${result.queryTime}ms`,
                        '平均插入耗时': `${result.avgInsertTime}ms/节点`,
                        '平均查询耗时': `${result.avgQueryTime}ms/查询`,
                        '树深度': result.treeDepth,
                        '叶子节点数': result.leafNodes
                    });
                });
                
                updateStatus('可扩展性测试完成', 'completed');
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                updateProgress(-1);
                isRunning = false;
            }
        }
        
        // 对比测试（不同配置参数）
        async function runComparisonTest() {
            if (isRunning) return;
            isRunning = true;
            
            const nodeCount = parseInt(document.getElementById('nodeCount').value);
            const worldSize = parseInt(document.getElementById('worldSize').value);
            
            updateStatus('正在进行配置对比测试...');
            updateProgress(0);
            
            try {
                const configs = [
                    { maxNodes: 5, maxDepth: 4, name: '小象限-浅深度' },
                    { maxNodes: 10, maxDepth: 6, name: '中象限-中深度' },
                    { maxNodes: 20, maxDepth: 8, name: '大象限-深深度' },
                    { maxNodes: 50, maxDepth: 3, name: '超大象限-浅深度' }
                ];
                
                for (let i = 0; i < configs.length; i++) {
                    const config = configs[i];
                    updateStatus(`测试配置: ${config.name}`);
                    
                    const bounds = { minX: 0, minY: 0, maxX: worldSize, maxY: worldSize };
                    const quadTree = createQuadTree(bounds, config.maxNodes, config.maxDepth);
                    const nodes = generateRandomNodes(nodeCount, worldSize);
                    
                    // 插入测试
                    const insertStart = performance.now();
                    nodes.forEach(node => quadTree.insert(node));
                    const insertTime = performance.now() - insertStart;
                    
                    // 查询测试
                    const queries = generateRandomQueries(500, worldSize, 1000);
                    const queryStart = performance.now();
                    queries.forEach(query => quadTree.query(query));
                    const queryTime = performance.now() - queryStart;
                    
                    const stats = quadTree.getStats();
                    
                    addResultCard(`配置对比 - ${config.name}`, {
                        '最大节点数': config.maxNodes,
                        '最大深度': config.maxDepth,
                        '插入耗时': `${insertTime.toFixed(2)}ms`,
                        '查询耗时': `${queryTime.toFixed(2)}ms`,
                        '实际深度': stats.depth,
                        '叶子节点数': stats.leafNodes,
                        '插入效率': `${(nodeCount / insertTime * 1000).toFixed(0)} 节点/秒`
                    });
                    
                    updateProgress(((i + 1) / configs.length) * 100);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
                
                updateStatus('配置对比测试完成', 'completed');
                
            } catch (error) {
                updateStatus(`测试失败: ${error.message}`, 'error');
            } finally {
                updateProgress(-1);
                isRunning = false;
            }
        }
        
        // 禁用/启用按钮
        function toggleButtons(disabled) {
            const buttons = ['insertBtn', 'queryBtn', 'scaleBtn', 'compareBtn'];
            buttons.forEach(id => {
                document.getElementById(id).disabled = disabled;
            });
        }
        
        // 监听运行状态变化
        setInterval(() => {
            toggleButtons(isRunning);
        }, 100);
    </script>
</body>
</html>

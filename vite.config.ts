import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { defineConfig, loadEnv } from 'vite'

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
  plugins: [
    vue({
      template: {
        transformAssetUrls: {
          includeAbsolute: false,
        },
      },
    }),
    vueJsx()
  ],
  server: {
    port: parseInt(env.PORT || '3001'),
    open: true,
      hmr: false, //关闭热更新
    host: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  },
  // 支持直接运行TS文件
  optimizeDeps: {
    include: ['src/**/*.ts']
  }
  }
})

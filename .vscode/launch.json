{"version": "0.2.0", "configurations": [{"name": "运行当前TS文件", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/run-ts.js", "args": ["--current", "${file}"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}, {"name": "运行默认测试文件", "type": "node", "request": "launch", "program": "${workspaceFolder}/scripts/run-ts.js", "args": ["test.ts"], "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}
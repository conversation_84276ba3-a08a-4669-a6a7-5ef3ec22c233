#!/usr/bin/env node

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

const execAsync = promisify(exec);
// 从环境变量获取端口，默认3001
const port = process.env.PORT || getPort() || 3001;
const baseUrl = `http://localhost:${port}/`;

// 获取命令行参数
const args = process.argv.slice(2);
let filename = args[0];
if (!filename) {
  throw 'file error';
}

checkVite();

// 处理绝对路径，转换为相对路径
if (path.isAbsolute(filename)) {
  filename = path.relative(process.cwd(), filename);
}
console.log('>>>', filename, 'port', process.env.PORT);
if (isJSFile(filename)) {
  console.log(`🚀 正在运行 TypeScript 文件: ${filename}`);
  openUrl(`ts_index.html?file=${encodeURIComponent(filename)}`);
} else {
  openUrl(filename);
}

function isJSFile(filename) {
  return /\.(ts|js|jsx|tsx)$/i.test(filename);
}

function openUrl(url) {
  url = baseUrl + url;
  console.log(`📱 浏览器地址: ${url}`);
  // 在默认浏览器中打开
  const openCommand =
    process.platform === 'darwin' ? 'open' : process.platform === 'win32' ? 'start' : 'xdg-open';

  try {
    execAsync(`${openCommand} "${url}"`);
  } catch (error) {
    console.log('❌ 无法自动打开浏览器，请手动访问:', url);
  }
}

function getPort() {
  try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const portMatch = envContent.match(/^PORT=(.+)$/m);
    if (portMatch) return portMatch[1];
  } catch (e) {
    // .env文件不存在或读取失败，使用默认值
  }
}

//检查vite是否运行
async function checkVite() {
  try {
    // 检查开发服务器是否运行
    await execAsync(`curl -s ${baseUrl} > /dev/null 2>&1`);
    console.log('✅ 开发服务器已运行');
  } catch (error) {
    console.log('⚠️  开发服务器未运行，正在启动...');

    // 自动启动开发服务器
    try {
      console.log('🔄 正在启动开发服务器...');
      exec('pnpm dev', { cwd: process.cwd() });

      // 等待服务器启动
      for (let i = 0; i < 10; i++) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
        try {
          await execAsync(`curl -s ${baseUrl} > /dev/null 2>&1`);
          console.log('✅ 开发服务器启动成功');
          break;
        } catch (e) {
          if (i === 9) {
            console.log('❌ 开发服务器启动失败，请手动运行: pnpm dev');
            return;
          }
        }
      }
    } catch (startError) {
      console.log('❌ 无法启动开发服务器，请手动运行: pnpm dev');
      return;
    }
  }
}

{
  // "compileOnSave": true,
  "compilerOptions": {
    "target": "ES6",
    "module": "ES6",
    "lib": ["ES6", "DOM", "webworker"],
    "allowJs": true,
    "moduleResolution": "bundler",  // 支持现代打包工具的模块解析
    //确保每个文件可以单独编译, 开启后每个ts文件必须包含export 或者 import
    //    "isolatedModules": true,
    // 根项目不生成编译输出
    "noEmit": true,
    //不生成 .d.ts 类型声明文件
    "declaration": false,
    //不使用tslib
    "importHelpers": false,
    //解决CommonJS 模块使用es6引入问题
    "esModuleInterop": true,
    //跳过对第三方库文件的类型检查
    "skipLibCheck": true,
    //强制文件大小写
    "forceConsistentCasingInFileNames": true,
    //    "paths": {
    //      "shared/*": ["/packages/shared/*"]
    //    }
    //    "baseUrl": "."

    //严格模式下，代码写起来会很繁琐
    "strict": true,
    "jsx": "preserve",
    "jsxFactory": "JSX_h", // 代码会自动插入 /** @jsx JSX_h */ 但是需要自己引入 JSX_h
    "jsxFragmentFactory": "Fragment" // 代码会自动插入 /** @jsxFrag Fragment */, 如果不设置默认会使用React.Fragment
    //允许使用any
    //    "noImplicitAny": false,
    // 严格空检查
    //    "strictNullChecks": true,
    //    "strictFunctionTypes": true,         // 函数类型逆变检查
    //    "strictBindCallApply": true,         // bind/call/apply参数检查
    //    "strictPropertyInitialization": true // 类属性初始化检查
    // "jsx": "react"
  },
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.jsx",
    "**/*.js",
    //    "packages/**/*.tsx",
    //    "./packages/@types/*.d.ts"
  ],
  // 包含所有 TS 文件
  "exclude": ["node_modules", "dist", "**/dist/", "**/node_modules/"]
  // 排除 node_modules 和 dist 目录
}

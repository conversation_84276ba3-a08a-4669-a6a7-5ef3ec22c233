{"name": "ai", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "run:ts": "node scripts/run-ts.js", "run:hash": "node scripts/run-ts.js src/hashcode-performance.ts", "run:hash-utils": "node scripts/run-ts.js src/hashcode-utils.ts", "run:hash-benchmark": "node scripts/run-ts.js src/hashcode-benchmark.ts", "run:hash-quick": "node scripts/run-ts.js src/quick-hash-test.ts", "run:hash-3rd": "node scripts/run-ts.js src/third-party-hash-test.ts", "run:hash-ultra": "node scripts/run-ts.js src/ultra-fast-hash.ts", "run:hash-wasm": "node scripts/run-ts.js src/wasm-hash-concept.ts", "run:hash-million": "node scripts/run-ts.js src/million-ops-hash.ts"}, "devDependencies": {"@types/node": "^22.15.23", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "@vitejs/plugin-react": "^4.5.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5.8.3", "vite": "^5.4.19"}, "dependencies": {"@vue/compiler-sfc": "^3.5.16", "fast-json-stable-stringify": "^2.1.0", "konva": "^9.3.20", "murmurhash3js": "^3.0.1", "object-hash": "^3.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-konva": "^19.0.4", "vue": "^3.5.16", "xxhashjs": "^0.2.2"}}
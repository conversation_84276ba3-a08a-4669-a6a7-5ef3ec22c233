<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>动态运行ts/js by vite</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        position: absolute;
        inset: 0;
        /* display: flex; */
      }
    </style>
  </head>
  <body>
    <!-- <div id="performance-info" style="margin: 10px 0; font-family: monospace; color: #666"></div> -->
    <script type="module">
      let file = new URLSearchParams(window.location.search).get('file');
      if (file) {
        if (!file.startsWith('./') && !file.startsWith('../')) {
          file = './' + file;
        }
        const script = document.createElement('script');
        script.setAttribute('type', 'module');
        script.src = file;
        document.head.appendChild(script);

        //下面的方式可能会对js文件增加?import, 与原始url不同, 可能导致多次引用
        // try {
        //   console.log(`正在加载文件: ${file}`);
        //   await import(/* @vite-ignore */ file);
        //   console.log(`文件 ${file} 加载完成`);
        // } catch (error) {
        //   console.error(`导入文件 ${file} 失败:`, error);
        //   console.error('错误详情:', error.message);
        // }
      }
    </script>
  </body>
</html>

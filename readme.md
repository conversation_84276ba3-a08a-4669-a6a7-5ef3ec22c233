# 项目介绍

本项目使用canvas技术实现一套ui系统,使用声明式响应式组件, 参考Jetpack compose, swiftUI, Flutter, react，rejs

## 组件

使用函数组件,能自由组合,逻辑自定义

- 函数组件 - 指组件的定义和描述

- 组件对象 - 组件实例,对应节点树上的节点

种类: 基础组件(包含绘制和测量), 组合组件(使用jsx格式),包含属性和, 

- 返回值 - 函数组件的返回值, 

- node tree - 组件构成节点树


1, 检查四叉树代码,实现按范围(矩阵变换后)建立, 且支持动态增加和删除和搜索(按视口)
2, 检查gl_buffer代码, 实现对root下面节点的buffer数据的管理,并能生成需要的索引数据, 具体是: 根据孩子节点(以及孩子的孩子)信息构建顶点,颜色等数据, 叶子节点的gl数据分两类,一种是固定数据长度(比如矩形),一种是动态长度(比如多边形),数据按双向分配, 节点记录自己数据的位置和长度, buffer数据相对稳定, 而索引数据根据需要渲染的节点数组动态生成
3, 检查gl.ts, 实现视口内渲染root节点,  根据root节点和画布尺寸获取视口信息, 通过四叉树获取视口内可见节点数组, 然后获取这些节点的索引信息, 加上buffer数据和屏幕矩阵信息, 交给webgl进行渲染
4,  进一步提炼, 将从根据root和画布尺寸获取数据的功能封装成函数, gl.ts只需要保留根据数据和视口进行渲染的功能, 移除gl.ts中所有其他代码